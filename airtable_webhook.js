// Airtable script to send a webhook and log the return
const config = input.config();

// The webhook URL - removing any trailing slashes or spaces
const webhookUrl = 'https://hooks.airtable.com/workflows/v1/genericWebhook/appnTcc8uhzTcDJMY/wflR6kmsfaNFHYbsj/wtrLVlZVTXAOO7L2t'.trim();

// Try a minimal payload that Airtable might accept
const payload = {};

console.log('Webhook URL:', webhookUrl);
console.log('Sending minimal payload');

// Send the webhook
const response = await fetch(webhookUrl, {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify(payload)
});

// Log the response status and headers
console.log(`Response status: ${response.status}`);
console.log('Response headers:', JSON.stringify(Object.fromEntries([...response.headers]), null, 2));

// Parse and log the response body
try {
    const responseBody = await response.json();
    console.log('Response body:', JSON.stringify(responseBody, null, 2));
    
    // Return the response information
    return {
        status: response.status,
        body: responseBody
    };
} catch (error) {
    console.error('Error parsing response:', error);
    
    // Try to get text response if JSON parsing fails
    try {
        const textResponse = await response.text();
        console.log('Text response:', textResponse);
        return {
            status: response.status,
            text: textResponse
        };
    } catch (textError) {
        console.error('Error getting text response:', textError);
        return {
            status: response.status,
            error: error.message
        };
    }
} 