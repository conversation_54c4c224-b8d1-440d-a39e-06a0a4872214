// Airtable webhook testing script
const config = input.config();

// Function to verify webhook URL
async function testWebhook() {
    console.log('Starting webhook test...');
    
    // The webhook URL to test
    const webhookUrl = 'https://hooks.airtable.com/workflows/v1/genericWebhook/appnTcc8uhzTcDJMY/wflR6kmsfaNFHYbsj/wtrLVlZVTXAOO7L2t';
    
    console.log('Testing webhook URL:', webhookUrl);
    
    // First try a GET request to check if the endpoint exists
    try {
        console.log('Trying HEAD request to check if endpoint exists...');
        const headResponse = await fetch(webhookUrl, {
            method: 'HEAD',
        });
        
        console.log(`HEAD response status: ${headResponse.status}`);
        if (headResponse.status === 404) {
            console.log('The webhook URL returned 404 Not Found. This indicates the URL is incorrect or the webhook has been deleted.');
        }
    } catch (error) {
        console.error('Error checking webhook:', error.message);
    }
    
    // Try different payload formats
    const payloadOptions = [
        // Empty payload
        {},
        
        // Simple payload
        { "message": "Test message" },
        
        // Payload with data wrapper
        { "data": { "message": "Test message" } },
        
        // Payload with fields as Airtable often uses
        { "fields": { "message": "Test message" } }
    ];
    
    for (let i = 0; i < payloadOptions.length; i++) {
        const payload = payloadOptions[i];
        console.log(`\nTest #${i+1}: Trying payload:`, JSON.stringify(payload, null, 2));
        
        try {
            const response = await fetch(webhookUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(payload)
            });
            
            console.log(`Response status: ${response.status}`);
            
            // Get response text
            const responseText = await response.text();
            try {
                // Try to parse as JSON
                const responseJson = JSON.parse(responseText);
                console.log('Response body:', JSON.stringify(responseJson, null, 2));
            } catch {
                // If not JSON, just show text
                console.log('Response text:', responseText);
            }
            
            if (response.status !== 404) {
                console.log('✅ Found a working payload format!');
            }
        } catch (error) {
            console.error(`Error with payload option ${i+1}:`, error.message);
        }
    }
    
    console.log('\n----- TROUBLESHOOTING STEPS -----');
    console.log('1. Verify the webhook URL is correct - check for typos');
    console.log('2. Confirm the webhook is still active in your Airtable automation');
    console.log('3. Try recreating the webhook in Airtable to get a fresh URL');
    console.log('4. Check if the webhook requires specific fields in the payload');
    console.log('5. Ensure your Airtable permissions allow access to this webhook');
    
    return {
        message: "Webhook testing complete. See logs for details."
    };
}

// Run the test
return await testWebhook(); 