import { NextResponse } from 'next/server';
import { stripe } from '@/lib/stripe/admin'; // Your Stripe admin instance
import <PERSON><PERSON> from 'stripe';
import { createClient } from '@/lib/supabase/server'; // Import Supabase server client for user session
import { supabaseAdmin } from '@/lib/supabase/admin'; // Import Supabase admin client for profile lookup

export async function POST(req: Request) {
  try {
    // Destructure existingSubscriptionIdToCancel from the request body
    const { priceId, productId, existingSubscriptionIdToCancel } = await req.json();

    if (!priceId || !productId) {
      return NextResponse.json({ error: 'Missing priceId or productId' }, { status: 400 });
    }

    // Get authenticated user
    const supabase = await createClient();
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      console.error('Error fetching user or user not authenticated:', userError?.message);
      return NextResponse.json({ error: 'User not authenticated' }, { status: 401 });
    }

    const userEmail = user.email;
    const userId = user.id;

    if (!userEmail) {
        // This should ideally not happen if user object is present
        console.error('User object fetched but email is missing.', { userId });
        return NextResponse.json({ error: 'User email is missing after authentication.'}, { status: 500 });
    }

    // Fetch user's profile to get stripe_customer_id
    let stripeCustomerId: string | undefined = undefined;
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('stripe_customer_id')
      .eq('id', userId) // Use user.id to match profile
      .single();

    if (profileError && profileError.code !== 'PGRST116') { // PGRST116: row not found
      console.error('Error fetching profile:', profileError.message);
      // Don't necessarily block checkout creation, but log it. Stripe will create a customer.
    } else if (profile) {
      stripeCustomerId = profile.stripe_customer_id || undefined; 
    }

    const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';

    let priceDetails: Stripe.Price;
    try {
      priceDetails = await stripe.prices.retrieve(priceId as string);
    } catch (error) {
      console.error('Error retrieving price from Stripe:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to retrieve price details.';
      return NextResponse.json({ error: `Stripe API error: ${errorMessage}` }, { status: 500 });
    }

    const mode = priceDetails.type === 'recurring' ? 'subscription' : 'payment';
    
    const metadata: { [key: string]: string } = {
      productId: productId as string,
      type: mode === 'subscription' ? 'subscription' : 'once',
      userEmail: userEmail, // Always include userEmail in metadata for webhook
      userId: userId, // Include Supabase user ID for potential linking in webhook
    };

    if (existingSubscriptionIdToCancel) {
      metadata.existingSubscriptionIdToCancel = existingSubscriptionIdToCancel;
    }

    let sessionParams: Stripe.Checkout.SessionCreateParams = {
      line_items: [
        {
          price: priceId as string,
          quantity: 1,
        },
      ],
      mode: mode,
      metadata: metadata,
      success_url: `${siteUrl}/subscribe/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${siteUrl}/subscribe`,
    };

    // If we have a Stripe Customer ID from profiles, use it
    if (stripeCustomerId) {
      sessionParams.customer = stripeCustomerId;
    } else {
      // Otherwise, prefill email for Stripe to create/match customer
      sessionParams.customer_email = userEmail;
    }

    if (mode === 'subscription') {
      sessionParams.subscription_data = {
         metadata: metadata
      };
    } else if (mode === 'payment') {
      sessionParams.payment_intent_data = {
         metadata: metadata
      };
    }

    const session = await stripe.checkout.sessions.create(sessionParams);

    if (!session.id) {
        throw new Error('Stripe session ID is missing.')
    }

    return NextResponse.json({ sessionId: session.id });

  } catch (error) {
    console.error('Error creating Stripe Checkout session:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred.';
    return NextResponse.json({ error: `Server error: ${errorMessage}` }, { status: 500 });
  }
} 