import { NextResponse } from 'next/server';
import { stripe } from '@/lib/stripe/admin';
import <PERSON><PERSON> from 'stripe';

// Interfaces for the new API response structure (matching Stripe objects closely)
interface StripePriceResponse {
  id: string;
  unit_amount: number | null;
  currency: string;
  type: 'one_time' | 'recurring';
  recurring?: Stripe.Price.Recurring | null;
  // Add other fields from Stripe.Price if needed by the frontend
}

// Define the structure of Stripe marketing features
interface StripeMarketingFeature {
  name: string;
}

interface StripeProductResponse {
  id: string;
  name: string;
  description: string | null;
  images: string[];
  active: boolean;
  marketing_features?: string[]; // Store as string array for frontend simplicity
  metadata: {
    [key: string]: string; // Assuming display_order and product_group might be here
  };
  prices: StripePriceResponse[]; // Each product will have its prices nested
}

export async function GET() {
  let stripeProductsList: Stripe.ApiList<Stripe.Product>;
  try {
    console.log('[PRODUCTS_API] Fetching active products from Stripe...');
    stripeProductsList = await stripe.products.list({
      active: true,
      limit: 100, 
      expand: ['data.marketing_features'] // Expand marketing_features in the response
    });

    if (!stripeProductsList || !stripeProductsList.data) {
      console.log('[PRODUCTS_API] No products data found in Stripe response.');
      return NextResponse.json({ products: [] });
    }

    console.log(`[PRODUCTS_API] Fetched ${stripeProductsList.data.length} active products. Now fetching their prices...`);

    const productsWithPrices: StripeProductResponse[] = [];

    for (const product of stripeProductsList.data) {
      let stripePricesList: Stripe.ApiList<Stripe.Price>;
      try {
        console.log(`[PRODUCTS_API] Fetching prices for product: ${product.id} (${product.name})`);
        stripePricesList = await stripe.prices.list({
          product: product.id,
          active: true,
          limit: 10, 
        });

        const formattedPrices: StripePriceResponse[] = stripePricesList.data ? stripePricesList.data.map(price => ({
          id: price.id,
          unit_amount: price.unit_amount,
          currency: price.currency,
          type: price.type,
          recurring: price.recurring,
        })) : [];
        
        console.log(`[PRODUCTS_API] Fetched ${formattedPrices.length} prices for product ${product.id}.`);

        productsWithPrices.push({
          id: product.id,
          name: product.name,
          description: product.description,
          images: product.images,
          active: product.active,
          marketing_features: product.marketing_features ? 
            product.marketing_features.map((feature: any) => feature.name) : 
            [], // Extract feature names
          metadata: product.metadata as { [key: string]: string },
          prices: formattedPrices,
        });

      } catch (pricesError: any) {
        console.warn(`[PRODUCTS_API_WARN] Error fetching prices for product ${product.id}: ${pricesError.message}. Skipping this product's prices.`);
        productsWithPrices.push({
          id: product.id,
          name: product.name,
          description: product.description,
          images: product.images,
          active: product.active,
          marketing_features: product.marketing_features ? 
            product.marketing_features.map((feature: any) => feature.name) : 
            [], // Extract feature names
          metadata: product.metadata as { [key: string]: string },
          prices: [],
        });
        // continue; // No need to explicitly continue, loop will proceed
      }
    }
    
    productsWithPrices.sort((a, b) => {
        const orderA = a.metadata?.display_order ? parseInt(a.metadata.display_order, 10) : Infinity;
        const orderB = b.metadata?.display_order ? parseInt(b.metadata.display_order, 10) : Infinity;
        if (isNaN(orderA) && isNaN(orderB)) return 0;
        if (isNaN(orderA)) return 1; 
        if (isNaN(orderB)) return -1; 
        return orderA - orderB;
    });

    console.log(`[PRODUCTS_API] Successfully processed ${productsWithPrices.length} products with their prices.`);
    return NextResponse.json({ products: productsWithPrices });

  } catch (productsError: any) {
    console.error('[PRODUCTS_API_ERROR] Error fetching products from Stripe or processing them:', productsError.message);
    return NextResponse.json({ error: `Stripe API error: ${productsError.message}` }, { status: 500 });
  }
} 