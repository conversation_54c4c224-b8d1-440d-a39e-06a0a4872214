import { NextRequest, NextResponse } from 'next/server';
import { stripe } from '@/lib/stripe/admin';
import { supabaseAdmin } from '@/lib/supabase/admin';
import Stripe from 'stripe';

export async function POST(req: NextRequest) {
  try {
    const { 
      userId,
      subscriptionId, 
      subscriptionItemId, 
      newPriceId 
    } = await req.json();

    // Validate required parameters
    if (!subscriptionId || !subscriptionItemId || !newPriceId) {
      return NextResponse.json(
        { error: 'Missing required parameters: subscriptionId, subscriptionItemId, and newPriceId are required' },
        { status: 400 }
      );
    }

    // Get the current subscription to extract customer and current period end
    const currentSubscription = await stripe.subscriptions.retrieve(subscriptionId);
    if (!currentSubscription) {
      return NextResponse.json(
        { error: 'Subscription not found' },
        { status: 404 }
      );
    }

    // Get the new price details
    const newPrice = await stripe.prices.retrieve(newPriceId, { expand: ['product'] });
    if (!newPrice) {
      return NextResponse.json(
        { error: 'Price not found' },
        { status: 404 }
      );
    }

    // Calculate the start date for the new phase (current period end)
    const currentPeriodEnd = currentSubscription.current_period_end;
    
    // Create a subscription schedule from the existing subscription
    const subscriptionSchedule = await stripe.subscriptionSchedules.create({
      from_subscription: subscriptionId,
    });

    // Update the schedule to add a new phase starting at the next billing period
    const updatedSchedule = await stripe.subscriptionSchedules.update(subscriptionSchedule.id, {
      phases: [
        // Current phase (until current period end)
        {
          items: [
            {
              price: currentSubscription.items.data[0].price.id,
              quantity: currentSubscription.items.data[0].quantity || 1,
            }
          ],
          start_date: subscriptionSchedule.phases[0].start_date,
          end_date: currentPeriodEnd,
        },
        // New phase (starting at next billing period)
        {
          items: [
            {
              price: newPriceId,
              quantity: 1,
            }
          ],
          start_date: currentPeriodEnd,
          // Let it continue indefinitely (no end_date)
        }
      ],
      end_behavior: 'release', // Release the schedule after completion
    });

    // Get product information for response
    let productName = 'Updated Plan';
    let interval = null;
    
    if (newPrice && typeof newPrice.product !== 'string') {
      const productObject = newPrice.product as Stripe.Product;
      if (productObject && !('deleted' in productObject)) {
        productName = productObject.name || 'Updated Plan';
      }
      interval = newPrice.recurring?.interval || null;
    }

    // Store the scheduled change in the database
    if (userId) {
      const scheduledChangeData = {
        scheduled_subscription_id: subscriptionId,
        scheduled_product_id: typeof newPrice.product === 'string' ? newPrice.product : newPrice.product.id,
        scheduled_price_id: newPriceId,
        scheduled_interval: interval,
        scheduled_effective_date: new Date(currentPeriodEnd * 1000).toISOString(),
        scheduled_change_type: 'downgrade',
        updated_at: new Date().toISOString(),
      };

      const { error: updateError } = await supabaseAdmin
        .from('profiles')
        .update(scheduledChangeData)
        .eq('id', userId);

      if (updateError) {
        console.error('[SCHEDULE_SUBSCRIPTION_API] Error updating profile with scheduled change:', updateError.message);
        // Continue anyway - the Stripe schedule was created successfully
      }
    }

    // Prepare response
    const response = {
      success: true,
      subscriptionScheduleId: updatedSchedule.id,
      currentPeriodEnd: new Date(currentPeriodEnd * 1000).toISOString(),
      productName,
      interval,
      message: 'Subscription change scheduled successfully for the next billing period',
    };

    return NextResponse.json(response);
  } catch (error: any) {
    console.error('[SCHEDULE_SUBSCRIPTION_API_ERROR]', error);
    return NextResponse.json(
      { error: 'Failed to schedule subscription change: ' + error.message },
      { status: 500 }
    );
  }
}
