import { NextRequest, NextResponse } from 'next/server';
import { stripe } from '@/lib/stripe/admin';
import { supabaseAdmin } from '@/lib/supabase/admin';
import Stripe from 'stripe';

export async function POST(req: NextRequest) {
  try {
    const { 
      userId,
      subscriptionId, 
      subscriptionItemId, 
      newPriceId 
    } = await req.json();

    // Validate required parameters
    if (!subscriptionId || !subscriptionItemId || !newPriceId) {
      return NextResponse.json(
        { error: 'Missing required parameters: subscriptionId, subscriptionItemId, and newPriceId are required' },
        { status: 400 }
      );
    }

    // Update the subscription in Stripe - this will change the price but apply at the end of the billing period
    const updatedSubscription = await stripe.subscriptions.update(subscriptionId, {
      items: [
        {
          id: subscriptionItemId,
          price: newPriceId,
        }
      ],
      proration_behavior: 'create_prorations', // Changed from 'none' to enable immediate change with proration
    });

    // Fetch the updated price to get its full details
    let newPrice;
    try {
      newPrice = await stripe.prices.retrieve(newPriceId, { expand: ['product'] });
    } catch (error: any) {
      console.log('[UPDATE_SUBSCRIPTION_API] Error fetching new price details:', error.message);
      // Continue anyway - we at least successfully updated the subscription
    }

    let productName = 'Updated Plan';
    let interval = null;
    
    if (newPrice && typeof newPrice.product !== 'string') {
      // Check if product is a valid Stripe Product (not deleted)
      const productObject = newPrice.product as Stripe.Product;
      if (productObject && !('deleted' in productObject)) {
        productName = productObject.name || 'Updated Plan';
      }
      interval = newPrice.recurring?.interval || null;
    }

    // Prepare response object with proper typing
    interface ResponseData {
      success: boolean;
      subscriptionStatus: string;
      currentPeriodEnd: string;
      productName: string;
      interval: string | null;
      userEmail?: string;
    }

    let response: ResponseData = {
      success: true,
      subscriptionStatus: updatedSubscription.status,
      currentPeriodEnd: new Date(updatedSubscription.items.data[0].current_period_end * 1000).toISOString(),
      productName,
      interval
    };

    // If userId was provided, get the profile data
    if (userId) {
      // Find profile by user ID
      const { data: profile, error: profileError } = await supabaseAdmin
        .from('profiles')
        .select('email')
        .eq('id', userId)
        .single();

      if (profileError) {
        console.error('[UPDATE_SUBSCRIPTION_API] Error fetching profile:', profileError.message);
      } else if (profile) {
        response.userEmail = profile.email;
      }
    }

    return NextResponse.json(response);
  } catch (error: any) {
    console.error('[UPDATE_SUBSCRIPTION_API_ERROR]', error);
    return NextResponse.json(
      { error: 'Failed to update subscription: ' + error.message },
      { status: 500 }
    );
  }
} 