import { type EmailOtpType } from '@supabase/supabase-js'
import { type NextRequest } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { redirect } from 'next/navigation'

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const token_hash = searchParams.get('token_hash')
  const code = searchParams.get('code')
  const type = searchParams.get('type') as EmailOtpType | null
  const next = searchParams.get('next') ?? '/'

  console.log('Confirm route params:', { token_hash, code, type, next })

  if (token_hash && type) {
    const supabase = await createClient()
    const { error } = await supabase.auth.verifyOtp({
      type,
      token_hash,
    })
    console.log('OTP verification result:', { error })
    if (!error) {
      // redirect user to specified redirect URL or root of app with success message
      redirect(`${next}?message=Email confirmed! You are now logged in.&status=success`)
    }
  } else if (code) {
    // Handle code-based confirmation
    const supabase = await createClient()
    const { error } = await supabase.auth.exchangeCodeForSession(code)
    console.log('Code exchange result:', { error })
    if (!error) {
      // redirect user to specified redirect URL or root of app with success message
      redirect(`${next}?message=Email confirmed! You are now logged in.&status=success`)
    }
  }
  
  // redirect the user to an error page with some instructions
  console.log('Redirecting to error page')
  redirect('/auth/auth-code-error')
}
