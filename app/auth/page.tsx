'use client';

import { useState, FormEvent, useEffect, useRef, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { createClient } from '@/lib/supabase/client';
import { useAuthContext } from '@/contexts/AuthContext';
import SuccessAlert from '@/app/components/ui/SuccessAlert';
import { FloatingIndicator, Tabs, Button, TextInput, PasswordInput } from '@mantine/core';
// Style imports now come from theme.ts

export default function AuthPage() {  
  const router = useRouter();
  const searchParams = useSearchParams();
  const returnUrl = searchParams.get('returnUrl') || '/';
  
  const supabase = createClient();
  const { setIsAuthenticating } = useAuthContext();

  const [activeTab, setActiveTab] = useState<string>('login');
  const [showResetPasswordTab, setShowResetPasswordTab] = useState(false);
  const [resetPasswordStep, setResetPasswordStep] = useState<'request' | 'update'>('request');

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmNewPassword, setConfirmNewPassword] = useState('');
  const [name, setName] = useState('');
  const [statusMessage, setStatusMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [signupSuccess, setSignupSuccess] = useState(false);
  const [refsReady, setRefsReady] = useState(false);

  // Refs for floating indicator
  const rootRef = useRef<HTMLDivElement | null>(null);
  const controlsRefs = useRef<Record<string, HTMLButtonElement | null>>({});
  
  const setControlRef = useCallback((val: string) => (node: HTMLButtonElement | null) => {
    controlsRefs.current[val] = node;
  }, []);

  // Trigger a re-render once refs have been assigned after first mount
  useEffect(() => {
    // run only after initial mount, refs will be assigned before this effect runs
    setRefsReady(true);
  }, []);

  const clearFormStates = (clearEmail = false) => {
    if (clearEmail) setEmail('');
    setPassword('');
    setNewPassword('');
    setConfirmNewPassword('');
    setName('');
    // message is usually cleared separately or on tab change
  };
  
  // Session check - password reset detection removed
  useEffect(() => {
    const checkSession = async () => {
      try {
        // Get session
        const { data } = await supabase.auth.getSession();
        
        if (data?.session) {
          console.log('Session exists:', data.session);
          
          // If user is already logged in and has a returnUrl, redirect them
          if (returnUrl && returnUrl !== '/') {
            router.push(returnUrl);
          }
        }
      } catch (err) {
        console.error('Error checking session:', err);
      }
    };
    
    if (typeof window !== 'undefined') {
      checkSession();
    }
  }, [supabase, router, returnUrl]);

  const handleSignUp = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setStatusMessage('');
    setIsSubmitting(true);
    setSignupSuccess(false);
    setIsAuthenticating(true);
    
    // Construct the redirect URL for after confirmation
    const redirectTo = `${process.env.NEXT_PUBLIC_SITE_URL}/auth/confirm?next=${encodeURIComponent(returnUrl)}`;

    const { error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: redirectTo,
        data: { name },
      },
    });

    if (error) {
      setStatusMessage(`Error: ${error.message}`);
      setIsAuthenticating(false);
    } else {
      setStatusMessage('Check your email for the confirmation link!');
      setSignupSuccess(true);
      clearFormStates(true);
      setIsAuthenticating(false);
    }
    setIsSubmitting(false);
  };

  const handleSignIn = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setStatusMessage('');
    setIsSubmitting(true);
    setIsAuthenticating(true);

    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      setStatusMessage(`Error: ${error.message}`);
      setIsSubmitting(false);
      setIsAuthenticating(false);
      return;
    }
      
    // If there's a returnUrl, redirect there instead of home
    if (returnUrl && returnUrl !== '/') {
      router.push(returnUrl);
    } else {
      router.push('/');
    }
    // AuthContext will handle setIsAuthenticating(false) on navigation/session update
  };

  const handlePasswordResetRequest = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    if (!email) {
      setStatusMessage('Please enter your email address.');
      return;
    }
    setStatusMessage('');
    setIsSubmitting(true);
    
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/auth/reset-password`,
      });
      
      if (error) {
        setStatusMessage(`Error: ${error.message}`);
      } else {
        setStatusMessage('Password reset link sent! Check your email inbox.');
      }
    } catch (err) {
      console.error('Password reset error:', err);
      setStatusMessage('Error: An unexpected error occurred.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleUpdatePassword = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    if (newPassword !== confirmNewPassword) {
      setStatusMessage('Error: Passwords do not match.');
      return;
    }
    if (!newPassword) {
      setStatusMessage('Error: Password cannot be empty.');
      return;
    }
    setStatusMessage('');
    setIsSubmitting(true);
    
    // Password update functionality has been removed
    // Placeholder UI only
    
    setIsSubmitting(false);
    setStatusMessage('This functionality has been disabled.');
  };

  useEffect(() => {
    const { data: authListener } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('[AuthPage] Auth event:', event, session);
      
      if (event === "SIGNED_IN") {
        // For regular sign-ins or completed sign-ups, redirect is handled by signIn/signUp
        // setIsAuthenticating(false) is handled by AuthContext or page navigation
        console.log("Signed in event handled, router push or context should take over.");
      } else if (event === "SIGNED_OUT") {
        setActiveTab('login');
        setShowResetPasswordTab(false);
        clearFormStates(true);
      } else if (event === "PASSWORD_RECOVERY") {
        console.log("Password recovery event detected");
        
        // Set a cookie to indicate this is a password recovery session
        document.cookie = "sb-recovery-mode=true; path=/; max-age=1800"; // 30 minutes
        
        // If we're on the auth page when a password recovery link is clicked,
        // we should redirect to reset-password page
        router.push('/auth/reset-password');
      }
    });

    return () => {
      authListener?.subscription.unsubscribe();
    };
  }, [supabase, router, setIsAuthenticating]); // Add setIsAuthenticating if it's stable

  // Effect to clear message and manage form states when activeTab changes
  useEffect(() => {
    setStatusMessage('');
    setIsSubmitting(false);
    setSignupSuccess(false);

    // If not moving to resetPassword tab, ensure it's hidden and step is reset
    if (activeTab !== 'resetPassword') {
      setShowResetPasswordTab(false);
      setResetPasswordStep('request');
    }
    
    // Clear passwords when switching tabs, conditionally clear email
    if (activeTab === 'login') {
        clearFormStates(false); // Keep email if switching from signup/reset request
    } else if (activeTab === 'signup') {
        clearFormStates(false); // Keep email if switching from login
    } else if (activeTab === 'resetPassword') {
        if (resetPasswordStep === 'request') {
            // Keep email for the request form
            setPassword(''); setNewPassword(''); setConfirmNewPassword('');
        } else {
            // For update step, email might be from session context or can be cleared if needed
             setPassword(''); // Clear old main password field
        }
    }

  }, [activeTab, resetPasswordStep]); // Add resetPasswordStep dependency


  useEffect(() => {
    return () => {
      setIsAuthenticating(false); // Cleanup on unmount
    };
  }, [setIsAuthenticating]);
  


  return (
    <div className="h-screen flex flex-col justify-center items-center">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <h2 className="text-center text-2xl font-semibold mb-6">
          {activeTab === 'resetPassword' && resetPasswordStep === 'request' && 'Forgot Your Password?'}
          {activeTab === 'resetPassword' && resetPasswordStep === 'update' && 'Set New Password'}
          {activeTab === 'login' && 'Welcome back!'}
          {activeTab === 'signup' && 'Create a new account'}
        </h2>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md relative">
        <SuccessAlert />
        
        {/* Forms themselves will receive reduced opacity and pointer-events when submitting */}
     
        <Tabs value={activeTab} onChange={(value) => setActiveTab(value || 'login')} variant="none">
          <Tabs.List ref={rootRef}>
            <Tabs.Tab 
              value="login" 
              ref={setControlRef('login')}
            >
              Sign In
            </Tabs.Tab>
            <Tabs.Tab 
              value="signup" 
              ref={setControlRef('signup')}
            >
              Sign Up
            </Tabs.Tab>
            
              <Tabs.Tab 
                value="resetPassword" 
                ref={setControlRef('resetPassword')}
                  style={{ 
    opacity: activeTab !== 'resetPassword' ? 0 : 1,
    pointerEvents: activeTab !== 'resetPassword' ? 'none' : 'auto',
    display: activeTab !== 'resetPassword' ? 'none' : 'block'
  }}
              >
                Reset Password
              </Tabs.Tab>
            

            <FloatingIndicator
              target={refsReady && activeTab ? controlsRefs.current[activeTab] : null}
              parent={rootRef.current}
            />
          </Tabs.List>

            
          <Tabs.Panel value="login">
            <form onSubmit={handleSignIn} className={`space-y-6 ${isSubmitting ? 'opacity-50 pointer-events-none' : ''}`}>
              <div>
                <label htmlFor="login-email" className="sr-only">Email address</label>
                <div className="mt-1">
                  <TextInput
                    id="login-email"
                    name="email"
                    type="email"
                    autoComplete="email"
                    autoFocus
                    required
                    value={email}
                    onChange={(e) => setEmail(e.currentTarget.value)}
                    placeholder="Email address"
                    disabled={isSubmitting}
                    className="w-full"
                  />
                </div> 
              </div>
              <div>
                <label htmlFor="login-password" className="sr-only">Password</label>
                <div className="mt-1">
                  <PasswordInput
                    id="login-password"
                    name="password"
                    autoComplete="current-password"
                    required
                    value={password}
                    onChange={(e) => setPassword(e.currentTarget.value)}
                    placeholder="Password"
                    disabled={isSubmitting}
                    className="w-full"
                  />
                </div>
              </div>
              
              {statusMessage && (
                <p className={`${statusMessage.startsWith('Error:') ? 'text-red-600' : 'text-green-600'}`}>
                  {statusMessage}
                </p>
              )}
              <Button
                type="submit"
                variant="outline"
                color="black"
                custom-color="white"
                fullWidth
                loading={isSubmitting}
              >
                Sign In
              </Button>
              <div className="flex items-center justify-end">
                <Button
                  variant="link"
                  onClick={() => {
                    setStatusMessage('');
                    setShowResetPasswordTab(true);
                    setActiveTab('resetPassword');
                    setResetPasswordStep('request');
                  }}
                  disabled={isSubmitting}
                >
                  Forgot your password?
                </Button>
              </div>
            </form>
          </Tabs.Panel>

          <Tabs.Panel value="signup">
            <form onSubmit={handleSignUp} className={`space-y-6 ${isSubmitting ? 'opacity-50 pointer-events-none' : ''}`}>
              {!signupSuccess && (
                <>
                  <div>
                    <label htmlFor="signup-name" className="sr-only">Name</label>
                    <div className="mt-1">
                      <TextInput
                        id="signup-name"
                        name="name"
                        type="text"
                        autoComplete="name"
                        required
                        value={name}
                        onChange={(e) => setName(e.currentTarget.value)}
                        placeholder="Name"
                        disabled={isSubmitting}
                        className="w-full"
                      />
                    </div>
                  </div>
                  <div>
                    <label htmlFor="signup-email" className="sr-only">Email address</label>
                    <div className="mt-1">
                      <TextInput
                        id="signup-email"
                        name="email"
                        type="email"
                        autoComplete="email"
                        required
                        value={email}
                        onChange={(e) => setEmail(e.currentTarget.value)}
                        placeholder="Email address"
                        disabled={isSubmitting}
                        className="w-full"
                      />
                    </div>
                  </div>
                  <div>
                    <label htmlFor="signup-password" className="sr-only">Password</label>
                    <div className="mt-1">
                      <PasswordInput
                        id="signup-password"
                        name="password"
                        autoComplete="new-password"
                        required
                        value={password}
                        onChange={(e) => setPassword(e.currentTarget.value)}
                        placeholder="Password"
                        disabled={isSubmitting}
                        className="w-full"
                      />
                    </div>
                  </div>
                </>
              )}
              {statusMessage && (
                <p className={`${statusMessage.startsWith('Error:') ? 'text-red-600' : 'text-green-600'}`}>
                  {statusMessage}
                </p>
              )}
              {!signupSuccess && (
                <Button 
                  type="submit" 
                  variant="outline"
                  color="black"
                  custom-color="white"
                  fullWidth
                  loading={isSubmitting}
                >
                  Sign Up
                </Button>
              )}
            </form>
          </Tabs.Panel>

          {showResetPasswordTab && (
            <Tabs.Panel value="resetPassword">
              <div>
                {resetPasswordStep === 'request' ? (
                  <form onSubmit={handlePasswordResetRequest} className={`space-y-6 ${isSubmitting ? 'opacity-50 pointer-events-none' : ''}`}>
                    <p className="text-gray-600">Enter your email address and we'll send you a link to reset your password.</p>
                    <div>
                      <label htmlFor="reset-email" className="sr-only">Email address</label>
                      <div className="mt-1">
                        <TextInput
                          id="reset-email"
                          name="email"
                          type="email"
                          autoComplete="email"
                          required
                          value={email}
                          onChange={(e) => setEmail(e.currentTarget.value)}
                          placeholder="Email address"
                          disabled={isSubmitting}
                          className="w-full"
                        />
                      </div>
                    </div>
                    {statusMessage && (
                      <p className={`${statusMessage.startsWith('Error:') ? 'text-red-600' : 'text-green-600'}`}>
                        {statusMessage}
                      </p>
                    )}
                    <Button 
                      type="submit" 
                      variant="outline"
                      color="black"
                      custom-color="white"
                      fullWidth
                      loading={isSubmitting}
                    >
                      Send Password Reset Link
                    </Button>
                  </form>
                ) : (
                  <form onSubmit={handleUpdatePassword} className={`space-y-6 ${isSubmitting ? 'opacity-50 pointer-events-none' : ''}`}>
                    <p className="text-gray-600">Please enter your new password.</p>
                    <div>
                      <label htmlFor="new-password" className="sr-only">New Password</label>
                      <div className="mt-1">
                        <PasswordInput
                          id="new-password"
                          name="newPassword"
                          required
                          value={newPassword}
                          onChange={(e) => setNewPassword(e.currentTarget.value)}
                          placeholder="New Password"
                          disabled={isSubmitting}
                          className="w-full"
                        />
                      </div>
                    </div>
                    <div>
                      <label htmlFor="confirm-new-password" className="sr-only">Confirm New Password</label>
                      <div className="mt-1">
                        <PasswordInput
                          id="confirm-new-password"
                          name="confirmNewPassword"
                          required
                          value={confirmNewPassword}
                          onChange={(e) => setConfirmNewPassword(e.currentTarget.value)}
                          placeholder="Confirm New Password"
                          disabled={isSubmitting}
                          className="w-full"
                          classNames={{
                            input: formStyles.input,
                            root: formStyles.root,
                          }}
                        />
                      </div>
                    </div>
                    {statusMessage && (
                      <p className={`${statusMessage.startsWith('Error:') ? 'text-red-600' : 'text-green-600'}`}>
                        {statusMessage}
                      </p>
                    )}
                    <Button 
                      type="submit" 
                      variant="outline"
                      color="black"
                      custom-color="white"
                      fullWidth
                      loading={isSubmitting}
                      disabled={!newPassword || !confirmNewPassword}
                    >
                      Update Password
                    </Button>
                  </form>
                )}
              </div>
            </Tabs.Panel>
          )}
        </Tabs>
      </div>
    </div>
  );
} 
