'use client';

import { useUserData } from '@/contexts/UserDataContext';

export default function UserProfileInfo() {
  const { 
    user, 
    userProfile, 
    activeSubscription, 
    purchasedProductIds, 
    isLoading, 
    error, 
    refreshUserData 
  } = useUserData();

  if (isLoading) {
    return <div className="loading">Loading user data...</div>;
  }

  if (error) {
    return (
      <div className="error">
        <p>Error: {error}</p>
        <button onClick={refreshUserData}>Retry</button>
      </div>
    );
  }

  if (!user) {
    return <div>Please sign in to view your profile</div>;
  }

  return (
    <div className="user-profile">
      <h2>User Profile</h2>
      <p>Email: {user.email}</p>
      
      <h3>Subscription Status</h3>
      {activeSubscription ? (
        <div>
          <p>Status: {activeSubscription.status}</p>
          <p>Product: {activeSubscription.productId}</p>
          <p>Plan: {activeSubscription.priceId}</p>
        </div>
      ) : (
        <p>No active subscription</p>
      )}
      
      <h3>Purchased Products</h3>
      {purchasedProductIds.length > 0 ? (
        <ul>
          {purchasedProductIds.map(id => (
            <li key={id}>{id}</li>
          ))}
        </ul>
      ) : (
        <p>No purchased products</p>
      )}
      
      <button onClick={refreshUserData}>Refresh Data</button>
    </div>
  );
}