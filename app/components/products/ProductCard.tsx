'use client';

import React from 'react';
import { Card, Badge, Text, Group, Stack, Button } from '@mantine/core';
import { Price, Product, ActiveSubscriptionDetails, getNormalizedMonthlyPrice } from '@/app/utils/pricingUtils';
import classes from '@/app/theme/Buttons.module.css';

// Copied from app/subscribe/page.tsx
// export interface Price { ... }
// export interface Product { ... }
// interface ActiveSubscriptionDetails { ... }
// End copied types

interface UserProfile {
  id: string;
  email: string | null;
  stripe_customer_id: string | null;
  stripe_subscription_id: string | null;
  subscription_status: string | null;
  subscribed_product_id: string | null;
  subscription_price_id: string | null;
  subscription_interval: string | null;
  subscription_current_period_end: string | null;
  cancel_at_period_end: boolean | null;
  purchases: string[] | null;
}

interface ProductCardProps {
  product: Product;
  isSubscriptionProduct: boolean;
  currentSelectedInterval?: string;
  activeSub: ActiveSubscriptionDetails | null;
  purchasedProductIds: string[];
  subscribingPriceId: string | null;
  loadingUserData: boolean;
  userEmail: string | null;
  allProducts: Product[]; // For getActiveSubscriptionPrice
  onSubscribeClick: (priceId: string, productId: string) => void;
  // getClientIntervalOrderValue is simple enough to be inlined or passed if complex
  showCancelButton?: boolean;
  onCancelClick?: () => void; // Modified to take no arguments
  userProfile?: UserProfile | null;
}

const ProductCard: React.FC<ProductCardProps> = ({
  product,
  isSubscriptionProduct,
  currentSelectedInterval,
  activeSub,
  purchasedProductIds,
  subscribingPriceId,
  loadingUserData,
  userEmail,
  allProducts,
  onSubscribeClick,
  showCancelButton,
  onCancelClick,
  userProfile,
}) => {
  const isActiveSubscriptionPlan =
    isSubscriptionProduct &&
    product.id === activeSub?.productId &&
    product.prices.some(
      (price) =>
        price.id === activeSub?.priceId &&
        (activeSub?.status === 'active' || activeSub?.status === 'trialing')
    );

  const isSubscriptionCancelling = 
    isActiveSubscriptionPlan && 
    activeSub?.cancelAtPeriodEnd === true;

  const isOneTimeProductType = product.prices.every((p) => p.type === 'one_time');
  const hasBeenPurchasedAsOneTime =
    purchasedProductIds.includes(product.id) && isOneTimeProductType;

  const getClientIntervalOrderValue = (price: Price): number => {
    if (price.type === 'one_time') {
      return 5; 
    }
    if (price.recurring?.interval) {
      switch (price.recurring.interval.toLowerCase()) {
        case 'day': return 1;
        case 'week': return 2;
        case 'month': return 3;
        case 'year': return 4;
        default: return 4.5; 
      }
    }
    return 4.5; 
  };


  let pricesToDisplay = product.prices;
  if (isSubscriptionProduct && currentSelectedInterval) {
    pricesToDisplay = product.prices.filter((price) => {
      return price.type === 'recurring' && price.recurring?.interval === currentSelectedInterval;
    });
  }
  const sortedPricesToDisplay = [...pricesToDisplay].sort(
    (a, b) => getClientIntervalOrderValue(a) - getClientIntervalOrderValue(b)
  );

  const findEquivalentMonthlyPrice = (productId: string, yearlyPrice: number): number | null => {
    const productForPrice = allProducts.find(p => p.id === productId); // Use allProducts prop
    if (!productForPrice) return null;
    const monthlyPrice = productForPrice.prices.find(
      (p) =>
        p.type === 'recurring' &&
        p.recurring?.interval === 'month' &&
        p.unit_amount !== null
    );
    return monthlyPrice?.unit_amount || null;
  };

  const calculateSavings = (yearlyAmount: number, monthlyAmount: number): number => {
    const yearlyTotal = yearlyAmount;
    const monthlyTotal = monthlyAmount * 12;
    return Math.round(((monthlyTotal - yearlyTotal) / monthlyTotal) * 100);
  };

  const getActiveSubscriptionPrice = (): Price | null => {
    if (!activeSub?.priceId) return null;
    for (const p of allProducts) {
      const foundPrice = p.prices.find((price) => price.id === activeSub.priceId);
      if (foundPrice) return foundPrice;
    }
    return null;
  };

  const activePrice = getActiveSubscriptionPrice();

  const getButtonProps = (price: Price) => {
    const isCurrentPriceOfActiveSubscription =
      isActiveSubscriptionPlan && price.id === activeSub?.priceId;
    const isPurchasedOneTime =
      hasBeenPurchasedAsOneTime && price.type === 'one_time' && product.id === product.id;
    const isProcessing = subscribingPriceId === price.id;

    let buttonText = 'Choose Plan';
    let buttonColor = 'white';

    if (isCurrentPriceOfActiveSubscription) {
      buttonText = 'Current Plan';
      buttonColor = 'white';
    } else if (isPurchasedOneTime) {
      buttonText = 'Purchased';
      buttonColor = 'white';
    } else if (isProcessing) {
      buttonText = 'Processing...';
      buttonColor = 'white';
    } else if (activePrice && activeSub?.status === 'active' && price.type === 'recurring') {
      const currentNormalizedPrice = getNormalizedMonthlyPrice(activePrice);
      const newNormalizedPrice = getNormalizedMonthlyPrice(price);
      
      if (currentNormalizedPrice === Infinity || newNormalizedPrice === Infinity) {
        // Default button text and color
      } else {
        const activeProductId = activeSub.productId;
        const isSameProduct = activeProductId === product.id;
        const isDifferentInterval =
          activePrice.recurring?.interval !== price.recurring?.interval;

        if (isSameProduct && isDifferentInterval) {
          buttonText = 'Change billing period';
          buttonColor = 'success';
        } else if (newNormalizedPrice > currentNormalizedPrice) {
          buttonText = 'Upgrade';
          buttonColor = 'success';
        } else if (newNormalizedPrice < currentNormalizedPrice) {
          buttonText = 'Downgrade';
          buttonColor = 'white';
        } else if (price.recurring?.interval === activePrice.recurring?.interval) {
          buttonText = 'Switch Plan';
          buttonColor = 'white';
        } else if (price.recurring?.interval && activePrice.recurring?.interval) {
          const currentIntervalOrder = getClientIntervalOrderValue(activePrice);
          const newIntervalOrder = getClientIntervalOrderValue(price);
          
          if (newIntervalOrder > currentIntervalOrder) {
            buttonText = 'Upgrade';
            buttonColor = 'success';
          } else if (newIntervalOrder < currentIntervalOrder) {
            buttonText = 'Downgrade';
            buttonColor = 'white';
          } else {
            buttonText = 'Switch Plan';
            buttonColor = 'white';
          }
        }
      }
    }

    const isDisabled =
      isCurrentPriceOfActiveSubscription ||
      isPurchasedOneTime ||
      isProcessing ||
      loadingUserData;

    return { buttonText, buttonColor, isDisabled };
  }; 

  return (
    <Card
      shadow="sm"
      padding="lg"
      radius="md"
      withBorder
      className={`h-full ${
        isActiveSubscriptionPlan 
          ? 'border-[3px] border-[hsl(var(--su))]' 
          : hasBeenPurchasedAsOneTime 
            ? 'border-[3px] border-[hsl(var(--p))]'
            : ''
      }`}
    >
      {product.images && product.images.length > 0 && (
        <Card.Section>
          <img
            src={product.images[0]}
            alt={product.name}
            className="w-full h-48 object-cover"
          />
        </Card.Section>
      )}

      <Stack gap="md" className="h-full"> {/* Added className="h-full" here */}
        {/* Prices Section */}
        {sortedPricesToDisplay.length > 0 ? (
          <Stack gap="xs">
            {sortedPricesToDisplay.map((price) => {
              const isCurrentPriceOfActiveSubscription =
                isActiveSubscriptionPlan && price.id === activeSub?.priceId;
              return (
                <div key={`${price.id}-display`}>
                  <Group justify="space-between" align="center">
                    <div>
                      <Text size="xl" fw={600} className={isCurrentPriceOfActiveSubscription ? 'text-[var(--mantine-color-green-7)]' : ''}>
                        ${(price.unit_amount ? price.unit_amount / 100 : 0).toFixed(2)}
                        <Text span size="sm" c="dimmed"> /{price.recurring?.interval || 'one-time'}</Text>
                      </Text>
                      {price.recurring?.interval === 'year' && price.unit_amount && (
                        <Text size="sm" c="dimmed">
                          ${((price.unit_amount / 12) / 100).toFixed(2)}/month
                          {(() => {
                            const monthlyEquivalent = findEquivalentMonthlyPrice(
                              product.id, 
                              price.unit_amount!
                            );
                            if (monthlyEquivalent) {
                              const savingsPercent = calculateSavings(
                                price.unit_amount!,
                                monthlyEquivalent
                              );
                              if (savingsPercent > 0) {
                                return (
                                  <Text span c="green.7" fw={500}>
                                    {' '}(–{savingsPercent}%)
                                  </Text>
                                );
                              }
                            }
                            return null;
                          })()}
                        </Text>
                      )}
                    </div>
                    {isCurrentPriceOfActiveSubscription && (
                      <Badge color="green" variant="light">Active</Badge>
                    )}
                  </Group>
                </div>
              );
            })}
          </Stack>
        ) : (
          <Text c="dimmed" ta="center" py="md">
            No pricing options available for this plan.
          </Text>
        )}

        {/* Plan Details Section */}
        <Stack gap="sm">
          <Text size="xl" fw={600}>{product.name}</Text>
          
          <Group>
            {hasBeenPurchasedAsOneTime && (
              <Badge color="violet" variant="light">Purchased</Badge>
            )}
            {isActiveSubscriptionPlan && !isSubscriptionCancelling && (
              <Badge color="green" variant="outline">Current Subscription</Badge>
            )}
            {isSubscriptionCancelling && (
              <Badge color="orange" variant="outline">Cancelling</Badge>
            )}
          </Group>

          {isSubscriptionCancelling && userProfile?.subscription_current_period_end && (
            <Text size="sm" c="orange.7" fw={500}>
              Subscription ends on {new Date(userProfile.subscription_current_period_end).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </Text>
          )}

          {product.marketing_features && product.marketing_features.length > 0 && (
            <Stack gap="xs">
              {product.marketing_features.map((feature, index) => (
                <Text key={index} size="sm" c="gray.7">• {feature}</Text>
              ))}
            </Stack>
          )}

          {product.description && (
            <Text size="sm" c="dimmed">{product.description}</Text>
          )}

          {product.metadata.product_group && (
            <Text size="sm" c="dimmed">Group: {product.metadata.product_group}</Text>
          )}
        </Stack>

        {/* Action Buttons Section */}
        {sortedPricesToDisplay.length > 0 && (
          <Stack gap="xs" mt="auto">
            {sortedPricesToDisplay.map((price) => {
              const { buttonText, buttonColor, isDisabled } = getButtonProps(price);
              return (
                <div key={`${price.id}-action`}>
                  <Button
                    onClick={() => onSubscribeClick(price.id, product.id)}
                    disabled={isDisabled}
                    fullWidth
                    custom-color={buttonColor}
                  >
                    {buttonText}
                  </Button>
                  {isActiveSubscriptionPlan && 
                   price.id === activeSub?.priceId && 
                   showCancelButton && 
                   onCancelClick && (
                    <Button
                      onClick={onCancelClick}
                      variant="white"
                      fullWidth
                      size="xs"
                      mt="xs"
                      custom-color="red"
                    >
                      Cancel Subscription
                    </Button>
                  )}
                </div>
              );
            })}
          </Stack>
        )}
      </Stack>
    </Card>
  );
};

export default ProductCard;
