'use client';

import React, { useState, useCallback, useEffect } from 'react';
import ProductCard from './ProductCard';
import { Button, Text, Title, Switch, Grid, Flex, Modal } from '@mantine/core';
import {
  Product,
  Price,
  getNormalizedMonthlyPrice,
} from '@/app/utils/pricingUtils';
import { getStripe } from '@/lib/stripe/client';
import { useUserData } from '@/contexts/UserDataContext';

interface Subscription {
  stripe_subscription_id: string;
  status: string;
  stripe_product_id: string | null;
  product_name?: string;
  current_period_end: string;
  cancel_at_period_end: boolean;
  user_id?: string;
}

interface OneTimePurchase {
  stripe_product_id: string;
  product_name?: string;
}

// --- Types for check-existing-subscription API response ---
interface PlanInfo {
  tier?: string;
  interval: string;
  productId: string;
  productName?: string;
  currentPeriodEnd?: string;
}

interface CheckSubscriptionResponse {
  isUpgrade: boolean;
  isDowngrade: boolean;
  isBillingPeriodChange?: boolean;
  currentSubscriptionId?: string;
  subscriptionItemId?: string;
  newPriceId?: string;
  currentPlanInfo?: PlanInfo;
  newPlanInfo?: PlanInfo;
  reason?: string;
  error?: string;
}
// --- End Types ---

export interface ProductListProps {
  listType: 'subscriptions' | 'addons';
  displayMode: 'all' | 'current' | 'upgrade';
  upgradeNumber?: number;
  showCancelButton?: boolean;
  onCancelClick?: (success: boolean) => void;
  onSubscriptionChange?: () => void;
  gridColumns?: 1 | 2 | 3 | 4;
  subscriptions?: Subscription[] | null;
  products?: Product[];
  purchases?: OneTimePurchase[] | null;
}

const findCheapestProductOverall = (
    products: Product[], 
    isSubscriptionCtx: boolean, 
    intervalCtx?: string
): Product | null => {
    const candidates = products
        .map(product => {
            let pricesToConsider = (isSubscriptionCtx && intervalCtx)
                ? product.prices.filter(p => p.type === 'recurring' && p.recurring?.interval === intervalCtx)
                : product.prices.filter(p => p.type === 'recurring');

            if (pricesToConsider.length === 0 && !isSubscriptionCtx && product.prices.some(p => p.type === 'one_time')) {
                pricesToConsider = product.prices.filter(p => p.type === 'one_time');
            }
            
            if (pricesToConsider.length === 0) return null;

            const priceInfos = pricesToConsider
                .map(price => ({
                    normalized: price.type === 'recurring' ? getNormalizedMonthlyPrice(price) : (price.unit_amount ?? Infinity)
                }))
                .filter(pi => pi.normalized !== Infinity)
                .sort((a, b) => a.normalized - b.normalized);

            return priceInfos.length > 0 ? { product, cheapestNormalizedPrice: priceInfos[0].normalized } : null;
        })
        .filter(candidate => candidate !== null) as { product: Product; cheapestNormalizedPrice: number }[];

    if (candidates.length === 0) return null;
    candidates.sort((a, b) => a.cheapestNormalizedPrice - b.cheapestNormalizedPrice);
    return candidates[0].product;
};

const ProductList: React.FC<ProductListProps> = ({
  listType,
  displayMode,
  upgradeNumber,
  showCancelButton,
  onCancelClick,
  onSubscriptionChange,
  gridColumns,
  subscriptions,
  products,
  purchases,
}) => {
  // Use the UserDataContext instead of local state for user data
  const { 
    user, 
    activeSubscription, 
    purchasedProductIds, 
    isLoading: userDataLoading, 
    error: userDataError,
    refreshUserData,
    userProfile
  } = useUserData();

  // Internal State for products data
  const [allFetchedProducts, setAllFetchedProducts] = useState<Product[]>([]);
  const [categorizedSubscriptionProducts, setCategorizedSubscriptionProducts] = useState<Product[]>([]);
  const [categorizedAddOnProducts, setCategorizedAddOnProducts] = useState<Product[]>([]);
  
  const [loadingStatus, setLoadingStatus] = useState({ products: true });
  const [componentError, setComponentError] = useState<string | null>(null);

  // State for subscription product interval selection (if listType is 'subscriptions')
  const [availableRecurringIntervals, setAvailableRecurringIntervals] = useState<string[]>([]);
  const [selectedInterval, setSelectedInterval] = useState<string>('');

  // State for modals and checkout process
  const [subscribingPriceId, setSubscribingPriceId] = useState<string | null>(null);
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const [upgradeDetails, setUpgradeDetails] = useState<CheckSubscriptionResponse | null>(null);
  const [checkoutParams, setCheckoutParams] = useState<{
    priceId: string;
    productId: string;
    existingSubscriptionIdToCancel?: string;
  } | null>(null);
  const [isConfirmingCheckout, setIsConfirmingCheckout] = useState(false);
  const [processingDowngrade, setProcessingDowngrade] = useState(false);
  const [downgradeSuccess, setDowngradeSuccess] = useState<{
    productName: string;
    currentPeriodEnd: string;
    isBillingPeriodChange?: boolean;
    newInterval?: string;
    newEffectiveDate?: string;
  } | null>(null);

  // State for cancellation modal
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [isCancelling, setIsCancelling] = useState(false);
  const [cancelSuccess, setCancelSuccess] = useState<{
    productName: string;
    currentPeriodEnd: string;
  } | null>(null);

  // Fetch All Products
  useEffect(() => {
    const fetchProducts = async () => {
      setLoadingStatus(prev => ({ ...prev, products: true }));
      try {
        const response = await fetch('/api/stripe/get-products');
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        setAllFetchedProducts(data.products || []);
      } catch (err) {
        setComponentError(err instanceof Error ? err.message : 'An unknown error occurred fetching products.');
      }
      setLoadingStatus(prev => ({ ...prev, products: false }));
    };
    fetchProducts();
  }, []);

  // Categorize products and set intervals once allFetchedProducts are available
  useEffect(() => {
    if (allFetchedProducts.length === 0) return;
    const subProducts = allFetchedProducts.filter(p => p.prices.some(price => price.type === 'recurring'));
    const oneTimeProds = allFetchedProducts.filter(p => p.prices.every(price => price.type === 'one_time') && !subProducts.find(sp => sp.id === p.id));
    
    const sortFn = (a: Product, b: Product) => {
        const getMinPrice = (p: Product) => Math.min(...(p.prices.map(price => price.unit_amount ?? Infinity).filter(pa => pa !== Infinity)), Infinity);
        return getMinPrice(a) - getMinPrice(b);
    };
    setCategorizedSubscriptionProducts([...subProducts].sort(sortFn));
    setCategorizedAddOnProducts([...oneTimeProds].sort(sortFn));

    if (listType === 'subscriptions' && subProducts.length > 0) {
      const intervals = new Set<string>();
      subProducts.forEach(p => p.prices.forEach(price => { if (price.type === 'recurring' && price.recurring?.interval) intervals.add(price.recurring.interval); }));
      const uniqueIntervals = Array.from(intervals).sort((a, b) => ({ month: 1, year: 2 }[a] || 3) - ({ month: 1, year: 2 }[b] || 3) || a.localeCompare(b));
      setAvailableRecurringIntervals(uniqueIntervals);
      if (uniqueIntervals.includes('month')) setSelectedInterval('month');
      else if (uniqueIntervals.includes('year')) setSelectedInterval('year');
      else if (uniqueIntervals.length > 0) setSelectedInterval(uniqueIntervals[0]);
      else setSelectedInterval('');
    } else if (listType !== 'subscriptions') {
        setAvailableRecurringIntervals([]);
        setSelectedInterval('');
    }
  }, [allFetchedProducts, listType]);
  
  // Determine products to actually display based on listType and selectedInterval
  const productsForCurrentListType = React.useMemo(() => {
    if (listType === 'subscriptions') {
      return categorizedSubscriptionProducts.filter(p => 
        p.prices.some(price => price.type === 'recurring' && price.recurring?.interval === selectedInterval)
      );
    }
    return categorizedAddOnProducts;
  }, [listType, categorizedSubscriptionProducts, categorizedAddOnProducts, selectedInterval]);

  // Handle showing cancel confirmation modal
  const handleCancelSubscription = useCallback(() => {
    if (!user) {
      console.error("ProductList: User not identified for cancellation.");
      setComponentError("User not identified for cancellation.");
      if (onCancelClick) onCancelClick(false);
      return;
    }
    if (!activeSubscription?.stripeSubscriptionId) {
      console.warn("ProductList: No stripe_subscription_id found in active subscription. Nothing to cancel.");
      setComponentError("No active Stripe subscription ID found to cancel.");
      if (onCancelClick) onCancelClick(true);
      return;
    }

    if (activeSubscription.status && !['active', 'trialing'].includes(activeSubscription.status)) {
        console.log(`ProductList: Subscription ${activeSubscription.stripeSubscriptionId} is not active or trialing (status: ${activeSubscription.status}). No cancellation needed.`);
        if (onCancelClick) onCancelClick(true);
        refreshUserData();
        if (onSubscriptionChange) onSubscriptionChange();
        return;
    }

    // Show confirmation modal
    setShowCancelModal(true);
    setComponentError(null);
    setCancelSuccess(null);
  }, [user, activeSubscription, refreshUserData, onSubscriptionChange, onCancelClick]);

  // Handle actual subscription cancellation after confirmation
  const confirmCancelSubscription = useCallback(async () => {
    if (!activeSubscription?.stripeSubscriptionId) return;

    setIsCancelling(true);
    setComponentError(null);
    const stripeSubscriptionIdToCancel = activeSubscription.stripeSubscriptionId;

    try {
      const response = await fetch('/api/stripe/cancel-subscription', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subscriptionId: stripeSubscriptionIdToCancel,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        const errorMessage = errorData.error || 'Server error during cancellation.';
        console.error("ProductList: Failed to cancel subscription:", errorMessage);
        setComponentError(`Failed to cancel subscription: ${errorMessage}`);
        if (onCancelClick) onCancelClick(false);
        return;
      }

      const responseData = await response.json();
      console.log(`ProductList: Stripe subscription ID ${stripeSubscriptionIdToCancel} cancellation request successful.`);
      
      // Show success state
      setCancelSuccess({
        productName: responseData.subscription?.product_name || 'Your subscription',
        currentPeriodEnd: responseData.subscription?.current_period_end || '',
      });

      await refreshUserData();
      if (onSubscriptionChange) onSubscriptionChange();
      if (onCancelClick) onCancelClick(true);

      // Auto-close success modal after 5 seconds
      setTimeout(() => {
        setShowCancelModal(false);
        setCancelSuccess(null);
      }, 5000);

    } catch (error: any) {
      console.error("ProductList: Error during subscription cancellation API call:", error.message);
      setComponentError("An unexpected error occurred while cancelling the subscription.");
      if (onCancelClick) onCancelClick(false);
    } finally {
      setIsCancelling(false);
    }
  }, [activeSubscription, refreshUserData, onSubscriptionChange, onCancelClick]);

  // Proceed to checkout
  const proceedToCheckout = useCallback(async () => {
    if (!checkoutParams) {
      setComponentError('Checkout parameters not set.');
      setIsConfirmingCheckout(false);
      return;
    }
    setIsConfirmingCheckout(true);
    setSubscribingPriceId(checkoutParams.priceId);
    setComponentError(null);
    setShowUpgradeModal(false);

    try {
      const body = { 
        ...checkoutParams, 
        userEmail: user?.email, 
        userId: user?.id 
      };
      
      const response = await fetch('/api/stripe/create-checkout-session', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body),
      });

      const sessionData = await response.json();
      if (!response.ok) throw new Error(sessionData.error || 'Failed to create checkout session.');
      
      if (sessionData.sessionId) {
        const stripe = await getStripe();
        if (stripe) {
          const { error: stripeError } = await stripe.redirectToCheckout({ sessionId: sessionData.sessionId });
          if (stripeError) setComponentError(stripeError.message || 'Failed to redirect to Stripe.');
        } else setComponentError('Stripe.js failed to load.');
      } else throw new Error('Session ID not received from server.');
      
      // Refresh user data after successful checkout
      if (onSubscriptionChange) onSubscriptionChange();
      
    } catch (err) {
      setComponentError(err instanceof Error ? err.message : 'An unknown error occurred during subscription.');
    } finally {
      setSubscribingPriceId(null);
      setCheckoutParams(null);
      setIsConfirmingCheckout(false);
    }
  }, [checkoutParams, user, onSubscriptionChange]);

  // Handle downgrade
  const handleDowngrade = async () => {
    if (!checkoutParams || !upgradeDetails || !upgradeDetails.isDowngrade || !upgradeDetails.currentSubscriptionId || !upgradeDetails.subscriptionItemId) {
      setComponentError('Invalid downgrade parameters.');
      return;
    }
    setIsConfirmingCheckout(true);
    setProcessingDowngrade(true);
    setComponentError(null);
    try {
      const response = await fetch('/api/stripe/update-subscription', {
      const response = await fetch('/api/stripe/schedule-subscription-change', {
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: user?.id,
          subscriptionId: upgradeDetails.currentSubscriptionId,
          subscriptionItemId: upgradeDetails.subscriptionItemId,
          newPriceId: checkoutParams.priceId,
        }),
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update subscription');
        throw new Error(errorData.error || 'Failed to schedule subscription change');
      const data = await response.json();
      setDowngradeSuccess({
        productName: data.productName || upgradeDetails.newPlanInfo?.productName || 'new plan',
        currentPeriodEnd: data.currentPeriodEnd || upgradeDetails.currentPlanInfo?.currentPeriodEnd || 'next billing period',
        isBillingPeriodChange: upgradeDetails.isBillingPeriodChange,
        newInterval: upgradeDetails.isBillingPeriodChange && upgradeDetails.newPlanInfo && typeof upgradeDetails.newPlanInfo.interval === 'string' 
                     ? upgradeDetails.newPlanInfo.interval 
                     : undefined,
        newEffectiveDate: upgradeDetails.isBillingPeriodChange ? (data.currentPeriodEnd || upgradeDetails.newPlanInfo?.currentPeriodEnd || 'next billing period') : undefined
        newEffectiveDate: data.currentPeriodEnd || upgradeDetails.newPlanInfo?.currentPeriodEnd || 'next billing period',
      
      // Refresh user data after successful downgrade
      await refreshUserData();
      if (onSubscriptionChange) onSubscriptionChange();
    } catch (err) {
      setComponentError(err instanceof Error ? err.message : 'An unknown error occurred during downgrade');
    } finally {
      setProcessingDowngrade(false);
      setIsConfirmingCheckout(false);
      setTimeout(() => {
        setShowUpgradeModal(false);
        setUpgradeDetails(null);
        setCheckoutParams(null);
        setSubscribingPriceId(null); 
      }, 5000); 
    }
  };

  // Handle subscribe click
  const handleSubscribeClick = async (priceId: string, productId: string) => {
    if (!user?.email || !user?.id) {
      // User is not logged in, redirect to auth page with return URL
      const returnUrl = `/subscribe?priceId=${priceId}&productId=${productId}`;
      window.location.href = `/auth?message=Please log in or sign up to subscribe&returnUrl=${encodeURIComponent(returnUrl)}`;
      return;
    }
    
    if (subscribingPriceId === priceId) return;

    setSubscribingPriceId(priceId);
    setComponentError(null);
    setCheckoutParams(null);
    setShowUpgradeModal(false);
    setUpgradeDetails(null);
    setDowngradeSuccess(null);

    try {
      const response = await fetch('/api/stripe/check-existing-subscription', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          userEmail: user.email, 
          newPriceId: priceId, 
          userId: user.id 
        }),
      });
      
      const checkData: CheckSubscriptionResponse = await response.json();
      if (!response.ok || checkData.error) {
        throw new Error(checkData.error || 'Failed to check existing subscription details.');
      }
      
      const currentSubProductId = activeSubscription?.productId;
      const currentSubPriceId = activeSubscription?.priceId;
      let currentSubInterval: string | undefined;
      if (currentSubProductId && currentSubPriceId) {
        currentSubInterval = userProfile?.subscription_interval ?? undefined;
        if (!currentSubInterval) {
            const currentProduct = allFetchedProducts.find(p => p.id === currentSubProductId);
            currentSubInterval = currentProduct?.prices.find(pr => pr.id === currentSubPriceId)?.recurring?.interval ?? undefined;
        }
      }

      const newProductDetails = allFetchedProducts.find(p => p.id === productId);
      const newPriceDetails = newProductDetails?.prices.find(p => p.id === priceId);
      const newInterval = newPriceDetails?.recurring?.interval;

      if (
        activeSubscription &&
        checkData.currentSubscriptionId &&
        checkData.subscriptionItemId &&
        currentSubProductId === productId && 
        currentSubInterval && newInterval && currentSubInterval !== newInterval
      ) {
        // This is a billing period change
        const currentPeriodEndValue = userProfile?.subscription_current_period_end;
        setUpgradeDetails({
          ...checkData,
          isBillingPeriodChange: true,
          isUpgrade: false,
          isDowngrade: false,
          currentPlanInfo: {
            ...(checkData.currentPlanInfo || {}),
            tier: checkData.currentPlanInfo?.tier || newProductDetails?.name,
            productId: currentSubProductId,
            productName: newProductDetails?.name, 
            interval: currentSubInterval,
            currentPeriodEnd: currentPeriodEndValue ?? undefined
          },
          newPlanInfo: {
            ...(checkData.newPlanInfo || {}),
            tier: checkData.newPlanInfo?.tier || newProductDetails?.name,
            productId: productId,
            productName: newProductDetails?.name,
            interval: newInterval,
          }
        });
        setCheckoutParams({
          priceId: priceId,
          productId: productId,
        });
        setShowUpgradeModal(true);
      } else if ((checkData.isUpgrade || checkData.isDowngrade) && checkData.currentSubscriptionId) {
        setUpgradeDetails(checkData);
        setCheckoutParams({
          priceId: priceId,
          productId: productId,
          existingSubscriptionIdToCancel: checkData.isUpgrade ? checkData.currentSubscriptionId : undefined,
        });
        setShowUpgradeModal(true);
      } else {
        setCheckoutParams({ priceId: priceId, productId: productId });
      }
    } catch (err) {
      setComponentError(err instanceof Error ? err.message : 'An unknown error occurred.');
      setSubscribingPriceId(null);
    }
  };

  useEffect(() => {
    if (checkoutParams && !showUpgradeModal && !processingDowngrade && subscribingPriceId === checkoutParams.priceId) {
      proceedToCheckout();
    }
  }, [checkoutParams, showUpgradeModal, subscribingPriceId, proceedToCheckout, processingDowngrade]);

  const handleModalConfirm = () => {
    if (!checkoutParams || !upgradeDetails) return;

    if (upgradeDetails.isBillingPeriodChange) {
      // Handle Billing Period Change confirmation
      if (!upgradeDetails.currentSubscriptionId || !upgradeDetails.subscriptionItemId || !checkoutParams.priceId) {
        setComponentError('Invalid parameters for billing period change.');
        return;
      }
      setIsConfirmingCheckout(true); // Use existing state for loading indication
      setProcessingDowngrade(true); // Can reuse or rename this state
      setComponentError(null);
      fetch('/api/stripe/update-subscription', { // Assuming this endpoint handles interval changes
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: user?.id,
          subscriptionId: upgradeDetails.currentSubscriptionId,
          subscriptionItemId: upgradeDetails.subscriptionItemId,
          newPriceId: checkoutParams.priceId, // Price ID for the new interval
        }),
      })
      .then(async response => {
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to update billing period');
        }
        return response.json();
      })
      .then(async data => {
        setDowngradeSuccess({
          productName: data.productName || upgradeDetails.newPlanInfo?.productName || 'Your Plan',
          currentPeriodEnd: data.currentPeriodEnd || 'updated successfully',
          isBillingPeriodChange: true,
          newInterval: upgradeDetails.newPlanInfo && typeof upgradeDetails.newPlanInfo.interval === 'string' 
                       ? upgradeDetails.newPlanInfo.interval 
                       : undefined,
          newEffectiveDate: data.currentPeriodEnd || upgradeDetails.newPlanInfo?.currentPeriodEnd || 'the next billing cycle',
        });
        await refreshUserData();
        if (onSubscriptionChange) onSubscriptionChange();
      })
      .catch(err => {
        setComponentError(err instanceof Error ? err.message : 'An unknown error occurred during billing period change');
      })
      .finally(() => {
        setProcessingDowngrade(false);
        setIsConfirmingCheckout(false);
        // setTimeout to close modal is already in existing downgrade logic, 
        // which will now also apply here after success/error. Consider if texts need adjustment.
        setTimeout(() => {
          setShowUpgradeModal(false);
          setUpgradeDetails(null);
          setCheckoutParams(null);
          setSubscribingPriceId(null); 
        }, 5000); 
      });
    } else if (upgradeDetails?.isDowngrade) {
      handleDowngrade(); // Existing downgrade logic
    } else if (!isConfirmingCheckout) { // Assumed to be an upgrade if not downgrade and not billing period change
      setIsConfirmingCheckout(true);
      proceedToCheckout();
    }
  };

  const handleModalCancel = () => {
    setShowUpgradeModal(false);
    setUpgradeDetails(null);
    setCheckoutParams(null);
    setSubscribingPriceId(null);
    setComponentError(null);
    setDowngradeSuccess(null);
  };

  const handleCancelModalClose = () => {
    setShowCancelModal(false);
    setIsCancelling(false);
    setComponentError(null);
    setCancelSuccess(null);
  };

  const UpgradeModalComponent = () => {
    if (!showUpgradeModal || !upgradeDetails) return null;
    const currentPlan = upgradeDetails.currentPlanInfo;
    const newPlan = upgradeDetails.newPlanInfo;
    const isDowngrade = upgradeDetails.isDowngrade;
    const isUpgrade = upgradeDetails.isUpgrade;
    const isBillingPeriodChange = upgradeDetails.isBillingPeriodChange;

    const formatDate = (dateString?: string) => {
      if (!dateString) return "the end of the current billing period";
      try { return new Date(dateString).toLocaleDateString(); } catch (e) { return dateString; }
    };

    const title = downgradeSuccess
      ? downgradeSuccess.isBillingPeriodChange ? "Billing Period Updated" : "Subscription Updated"
      : isBillingPeriodChange
        ? "Confirm Billing Period Change"
        : isDowngrade 
          ? "Confirm Downgrade" 
          : "Confirm Upgrade";

    return (
      <Modal 
        opened={showUpgradeModal} 
        onClose={handleModalCancel}

        centered
        withCloseButton={false}
      >
        <Modal.Body>
        <Modal.Title>{title}</Modal.Title>
          {downgradeSuccess ? (
            <>
              <Text>
                {downgradeSuccess.isBillingPeriodChange
                  ? `Your billing period for <strong>${downgradeSuccess.productName}</strong> has been changed to <strong>${downgradeSuccess.newInterval}</strong>. This change is effective immediately.`
                  : `Your plan will change to <strong>${downgradeSuccess.productName}</strong> on <strong>${formatDate(downgradeSuccess.currentPeriodEnd)}</strong>.`}
              </Text>
            </>
          ) : (
            <>
              {isBillingPeriodChange ? (
                <Text>
                  You are changing the billing period for <strong>{currentPlan?.productName}</strong> from <strong>{currentPlan?.interval}</strong> to <strong>{newPlan?.interval}</strong>.
                  This change will be effective immediately. Any remaining time on your current period will be credited, and you'll be charged for the new period on a prorated basis.
                </Text>
              ) : isDowngrade ? (
                <Text>
                  You are changing from <strong>{currentPlan?.productName}</strong> to <strong>{newPlan?.productName}</strong>.
                  This change will take effect at the end of your current billing period ({formatDate(currentPlan?.currentPeriodEnd)}).
                </Text>
              ) : isUpgrade ? (
                <Text>
                  You are upgrading from <strong>{currentPlan?.productName}</strong> to <strong>{newPlan?.productName}</strong>. 
                  {upgradeDetails.currentSubscriptionId ? (
                    <> Your current subscription will be canceled and you'll be charged for the new plan immediately.</>
                  ) : null}
                </Text>
              ) : null}
              {componentError && <Text className="text-red-500 mb-3">Error: {componentError}</Text>}
              <Flex justify="space-between" mt="lg">
                <Button variant="outline" custom-color="white" onClick={handleModalCancel}>Cancel</Button>
                <Button
                  custom-color={isDowngrade ? 'yellow' : 'black'}
                  onClick={handleModalConfirm}
                  loading={isConfirmingCheckout || processingDowngrade}
                >
                  {isConfirmingCheckout || processingDowngrade 
                    ? 'Processing...' 
                    : isBillingPeriodChange
                      ? 'Confirm Change'
                      : isDowngrade 
                        ? 'Confirm Downgrade' 
                        : 'Confirm & Proceed'}
                </Button>
              </Flex>
            </>
          )}
          </Modal.Body>
      </Modal>
    );
  };

  const CancellationModalComponent = () => {
    if (!showCancelModal) return null;

    const formatDate = (dateString: string) => {
      if (!dateString) return "the end of the current billing period";
      try { 
        return new Date(dateString).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        }); 
      } catch (e) { 
        return dateString; 
      }
    };

    const title = cancelSuccess ? "Subscription Cancelled" : "Cancel Subscription";
    const currentProduct = allFetchedProducts.find(p => p.id === activeSubscription?.productId);
    const productName = currentProduct?.name || "Your subscription";

    return (
      <Modal 
        opened={showCancelModal} 
        onClose={handleCancelModalClose}
        title={title}
        centered
        withCloseButton={false}
      >
        <Modal.Body>
          {cancelSuccess ? (
            <>
              <Text>
                <strong>{cancelSuccess.productName}</strong> has been cancelled and will remain active until <strong>{formatDate(cancelSuccess.currentPeriodEnd)}</strong>.
              </Text>
              <Text mt="sm" size="sm" c="dimmed">
                You'll continue to have access to all features until your billing period ends. No further charges will be made.
              </Text>
              <Flex justify="center" mt="lg">
                <Button
                  custom-color="white"
                  onClick={handleCancelModalClose}
                >
                  Got it
                </Button>
              </Flex>
            </>
          ) : (
            <>
              <Text>
                Are you sure you want to cancel <strong>{productName}</strong>?
              </Text>
              <Text mt="sm" size="sm" c="dimmed">
                Your subscription will remain active until the end of your current billing period ({userProfile?.subscription_current_period_end ? formatDate(userProfile.subscription_current_period_end) : 'current period end'}). You can reactivate anytime before then.
              </Text>
              {componentError && (
                <Text className="text-red-500 mt-3">Error: {componentError}</Text>
              )}
              <Flex justify="space-between" mt="lg">
                <Button 
                  variant="outline" 
                  custom-color="white" 
                  onClick={handleCancelModalClose}
                  disabled={isCancelling}
                >
                  Keep Subscription
                </Button>
                <Button
                  custom-color="red"
                  onClick={confirmCancelSubscription}
                  loading={isCancelling}
                >
                  {isCancelling ? 'Cancelling...' : 'Cancel Subscription'}
                </Button>
              </Flex>
            </>
          )}
        </Modal.Body>
      </Modal>
    );
  };

  let finalProductsToRender: Product[] = [];
  const upgradesToShow = upgradeNumber && upgradeNumber > 0 ? upgradeNumber : 1;

  if (loadingStatus.products || userDataLoading) {
    return <div className="container mx-auto p-4 text-center">Loading plans...</div>;
  }

  if (componentError && listType === 'subscriptions' && productsForCurrentListType.length === 0) {
      // If critical error (e.g. product fetch failed) and no products to show for subscriptions, display error prominently
      return <div className="container mx-auto p-4 text-center text-red-500">Error: {componentError}</div>;
  }
  
  if (displayMode === 'all') {
    finalProductsToRender = productsForCurrentListType;
  } else if (displayMode === 'current') {
    if (listType === 'subscriptions') {
      if (activeSubscription && activeSubscription.productId) {
        const currentProduct = productsForCurrentListType.find(p => p.id === activeSubscription.productId);
        if (currentProduct) finalProductsToRender = [currentProduct];
      }
    } else if (listType === 'addons' && purchasedProductIds) {
      finalProductsToRender = productsForCurrentListType.filter(p => purchasedProductIds.includes(p.id));
    }
  } else if (displayMode === 'upgrade') {
    if (activeSubscription && activeSubscription.priceId) {
      let currentActivePriceObject: Price | null = null;
      for (const prod of allFetchedProducts) {
        const found = prod.prices.find(p => p.id === activeSubscription.priceId);
        if (found) {
          currentActivePriceObject = found;
          break;
        }
      }

      if (currentActivePriceObject) {
        const currentSubscriptionNormalizedPrice = getNormalizedMonthlyPrice(currentActivePriceObject);
        const currentProductInDisplayList = productsForCurrentListType.find(p => p.id === activeSubscription.productId);

        if (currentProductInDisplayList) {
          if (!finalProductsToRender.find(p => p.id === currentProductInDisplayList.id)) {
            finalProductsToRender.push(currentProductInDisplayList);
          }
        }

        const upgradeCandidates = productsForCurrentListType
          .filter(p => p.id !== activeSubscription.productId)
          .map(product => {
            const relevantPrices = product.prices.filter(pr => pr.type === 'recurring' && (listType!=='subscriptions' || pr.recurring?.interval === selectedInterval));
            if (relevantPrices.length === 0) return null;
            const priceInfos = relevantPrices
              .map(price => ({ price, normalized: getNormalizedMonthlyPrice(price) }))
              .filter(pi => pi.normalized !== Infinity && pi.normalized > currentSubscriptionNormalizedPrice)
              .sort((a, b) => a.normalized - b.normalized);
            return priceInfos.length > 0 ? { product, bestUpgradePriceNormalized: priceInfos[0].normalized } : null;
          })
          .filter(candidate => candidate !== null) as { product: Product; bestUpgradePriceNormalized: number }[];

        if (upgradeCandidates.length > 0) {
          upgradeCandidates.sort((a, b) => a.bestUpgradePriceNormalized - b.bestUpgradePriceNormalized);
          const nextUpgradeProducts = upgradeCandidates.slice(0, upgradesToShow).map(uc => uc.product);
          nextUpgradeProducts.forEach(nextUpProduct => {
            if (!finalProductsToRender.find(p => p.id === nextUpProduct.id)) {
              finalProductsToRender.push(nextUpProduct);
            }
          });
        }
      } else {
        const cheapestProduct = findCheapestProductOverall(productsForCurrentListType, listType === 'subscriptions', selectedInterval);
        if (cheapestProduct) finalProductsToRender = [cheapestProduct];
      }
    } else {
      const cheapestProduct = findCheapestProductOverall(productsForCurrentListType, listType === 'subscriptions', selectedInterval);
      if (cheapestProduct) finalProductsToRender = [cheapestProduct];
    }
  }
  
  // --- Interval Toggle UI (Rendered if listType is 'subscriptions') ---
  const intervalSelectorUI = listType === 'subscriptions' && availableRecurringIntervals.length > 0 && (
    <Flex justify="flex-end" align="center" gap="md" mb="lg">
      {availableRecurringIntervals.length === 2 && availableRecurringIntervals.includes('month') && availableRecurringIntervals.includes('year') ? (
        <Flex align="center" gap="md">
          <span className={`${selectedInterval === 'month' ? 'text-gray-900 font-medium' : 'text-gray-500'}`}>Monthly</span>
          <Switch
            id={`interval-toggle-${listType}`}
            checked={selectedInterval === 'year'}
            onChange={() => setSelectedInterval(prev => prev === 'year' ? 'month' : 'year')}
            aria-label="Toggle billing interval"
          />
          <span className={`${selectedInterval === 'year' ? 'text-green-500 font-medium' : 'text-gray-500'}`}>Yearly</span>
        </Flex>
      ) : (
        <div className="dropdown dropdown-end">
          <label tabIndex={0} className="btn btn-outline btn-primary m-1 capitalize">{selectedInterval || 'Select'} <span className="ml-1">▼</span></label>
          <ul tabIndex={0} className="dropdown-content menu p-2 shadow bg-base-100 rounded-box w-52 z-[50]">
            {availableRecurringIntervals.map(interval => (<li key={interval}><a onClick={() => setSelectedInterval(interval)} className={selectedInterval === interval ? 'active' : ''}>{interval.charAt(0).toUpperCase() + interval.slice(1)}</a></li>))}
          </ul>
        </div>
      )}
    </Flex>
  );

  if (finalProductsToRender.length === 0) {
    return (
        <div className="text-center py-5 bg-blue-500">
            {intervalSelectorUI}
            <p className="text-gray-500">
                {displayMode === 'current' && "No active plan to display for current selection."}
                {displayMode === 'upgrade' && "No relevant plans for upgrade/current view based on selection."}
                {displayMode === 'all' && (listType === 'subscriptions' ? "No subscription plans available for this interval." : "No add-on products available.")}
            </p>
        </div>
    );
  }

  return (
    <>
      {componentError && <div className="mb-4 p-3 bg-red-100 text-red-700 border border-red-400 rounded text-center">Error: {componentError}</div>} 
      {intervalSelectorUI}
      <Grid gutter="lg">
        {finalProductsToRender.map(product => (
          <Grid.Col 
            key={product.id} 
            span={{ base: 12, sm: gridColumns === 1 ? 12 : 6, md: 12 / (gridColumns || 3) }}
          >
            <ProductCard
              product={product}
              isSubscriptionProduct={listType === 'subscriptions'}
              currentSelectedInterval={listType === 'subscriptions' ? selectedInterval : undefined}
              activeSub={activeSubscription}
              purchasedProductIds={purchasedProductIds}
              subscribingPriceId={subscribingPriceId}
              loadingUserData={userDataLoading}
              userEmail={user?.email || null}
              allProducts={allFetchedProducts}
              onSubscribeClick={handleSubscribeClick}
              showCancelButton={product.id === activeSubscription?.productId && listType === 'subscriptions' && showCancelButton}
              onCancelClick={product.id === activeSubscription?.productId && listType === 'subscriptions' && showCancelButton ? () => handleCancelSubscription() : undefined}
              userProfile={userProfile}
            />
          </Grid.Col>
        ))}
        </Grid>
      {showUpgradeModal && <UpgradeModalComponent />}
      {showCancelModal && <CancellationModalComponent />}
    </>
  );
};

export default ProductList; 
