'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter, usePathname } from 'next/navigation';
import { createClient } from '@/lib/supabase/client';
import type { User } from '@supabase/supabase-js';
import SignOutButton from '@/app/components/SignOutButton';
import { useAuthContext } from '@/contexts/AuthContext';
import { NavLink, Button, Menu, Group, Avatar, Text, ActionIcon } from '@mantine/core';
import { IconChevronDown, IconDashboard, IconLogout } from '@tabler/icons-react';

export default function UserNav() {
  const supabase = createClient();
  const router = useRouter();
  const pathname = usePathname();
  const [user, setUser] = useState<User | null>(null);
  const [loadingInitial, setLoadingInitial] = useState(true);
  const { isAuthenticating } = useAuthContext();

  useEffect(() => {
    async function getUserSession() {
      const { data: { session } } = await supabase.auth.getSession();
      if (session) {
        setUser(session.user);
      }
      setLoadingInitial(false);
    }

    getUserSession();

    const { data: authListenerData } = supabase.auth.onAuthStateChange(
      async (_event, session) => {
        setUser(session?.user ?? null);
        setLoadingInitial(false);
      }
    );

    return () => {
      authListenerData?.subscription.unsubscribe();
    };
  }, []); 

  if (isAuthenticating) {
    return (
      <nav className="fixed top-4 right-4 text-white p-4 w-[15em]">
        {/* Optionally, show a very minimal loading indicator or just nothing */}
      </nav>
    );
  }

  return (
    <nav className="fixed top-4 right-4 w-auto z-50">
      {loadingInitial ? (
        <p>Loading...</p>
      ) : user ? (
        <Group justify="center">
          <Menu
            withArrow
            width={260}
            position="bottom-end"
            transitionProps={{ transition: 'pop-top-right' }}
            withinPortal
            radius="xl"
          >
            <Menu.Target>
              <Button 
                variant="outline"
                color="black"
                rightSection={<IconChevronDown size={16} stroke={1.5} />}
              >
                <Group gap="xs">
                  <Text size="sm" fw={500}>{user.user_metadata?.name || user.email}</Text>
                </Group>
              </Button>
            </Menu.Target>
            <Menu.Dropdown>
              <Menu.Item 
                component={Link}
                href="/dashboard"
                leftSection={<IconDashboard size={16} stroke={1.5} />}
              >
                Dashboard
              </Menu.Item>
              
              <Menu.Divider />

              <Menu.Label>Account</Menu.Label>
              <Menu.Item disabled>
                 <Text size="xs" c="dimmed">
                    Signed in as {user.user_metadata?.name || user.email}
                    {user.email && user.user_metadata?.name && ` (${user.email})`}
                  </Text>
              </Menu.Item>
              <Menu.Item
                color="blue"
                leftSection={<IconLogout size={16} stroke={1.5} />}
                onClick={async () => {
                  await supabase.auth.signOut();
                  router.push('/');
                }}
              >
                Sign Out
              </Menu.Item>
            </Menu.Dropdown>
          </Menu>
        </Group>
      ) : (
        pathname && !pathname.startsWith('/auth') && (
          <NavLink 
            href="/auth" 
            label="Sign In / Sign Up" 
            component={Link} 
          />
        )
      )}
    </nav>
  );
} 