'use client';

import { useState, useTransition } from 'react';
import { updateUserPassword } from './actions'; // Assuming actions.ts is in the same directory

export default function ChangePasswordForm() {
  const [message, setMessage] = useState<{ text: string; type: 'success' | 'error' } | null>(null);
  const [isPending, startTransition] = useTransition();

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setMessage(null);
    const formData = new FormData(event.currentTarget);

    startTransition(async () => {
      const result = await updateUserPassword(formData);
      if (result.success) {
        setMessage({ text: result.message, type: 'success' });
        (event.target as HTMLFormElement).reset(); // Reset form on success
      } else {
        setMessage({ text: result.message, type: 'error' });
      }
    });
  };

  return (
    <div>
      <h2 className="mb-[.5em]">Change Password</h2>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="newPassword" className="sr-only">
            New Password
          </label>
          <input
            type="password"
            name="newPassword"
            id="newPassword"
            placeholder="Enter new password"
            required
            className="input input-bordered w-full"
          />
        </div>
        <div>
          <label htmlFor="confirmPassword" className="sr-only">
            Confirm New Password
          </label>
          <input
            type="password"
            name="confirmPassword"
            id="confirmPassword"
            placeholder="Confirm new password"
            required
            className="input input-bordered w-full"
          />
        </div>
        
        <button
          type="submit"
          disabled={isPending}
          className="btn btn-primary w-full"
        >
          {isPending ? <><span className="loading loading-spinner"></span>Updating Password...</> : 'Change Password'}
        </button>

        {message && (
          <div 
            role="alert"
            className={`alert mt-4  ${ 
              message.type === 'success' ? 'alert-success' : 'alert-error'
            }`}
          >
            <span>{message.text}</span>
          </div>
        )}
      </form>
    </div>
  );
} 