'use client';

import React, { useState, useEffect, lazy, Suspense, useRef, useCallback } from 'react';
import { FloatingIndicator, Tabs } from '@mantine/core';
import SubscriptionManagement from './SubscriptionManagement';
import ChangePasswordForm from './ChangePasswordForm';
import DeleteAccountButton from './DeleteAccountButton';
import ProductManagement from './admin/ProductManagement';
import UserManagement from './admin/UserManagement';
import { Product } from '@/app/utils/pricingUtils';

// Prop types are now more complex due to nested subscriptions
// It might be better to define these in a shared types file

// Define the structure for a single subscription (used in both props)
interface Subscription {
  stripe_subscription_id: string;
  status: string;
  stripe_product_id: string | null;
  product_name?: string;
  current_period_end: string;
  cancel_at_period_end: boolean;
  user_id?: string; // user_id might not always be present depending on context
}

// Structure for subscriptions WITHIN the User object for the table
// UserSubscription interface was here and is now removed.

// User structure for the table
// User interface was here and is now removed.

// One-time purchase structure
interface OneTimePurchase {
  stripe_product_id: string;
  product_name?: string;
}

interface DashboardTabsProps {
  // users: User[]; // User type updated here - REMOVED
  isAdmin: boolean; // Changed from currentUserIsAdmin
  loggedInUserId: string;
  // subscription: Subscription | null; // REMOVE old prop
  // productName: string; // REMOVE old prop
  subscriptions: Subscription[] | null; // NEW: Array of subscriptions for the logged-in user
  products: Product[]; // NEW: Array of relevant products
  purchases?: OneTimePurchase[] | null; // Add one-time purchases
}

// Add "Product Management" to TabName
type TabName = "User Management" | "Subscription" | "Product Management" | "Change Password" | "Delete Account";

// Helper to get available tabs based on admin status
const getAvailableTabs = (isAdmin: boolean): TabName[] => {
  // Define all possible tabs
  const commonTabs: TabName[] = ["Subscription", "Change Password", "Delete Account"];
  const adminTabs: TabName[] = ["User Management", "Product Management"];

  if (isAdmin) {
    // Common tabs first, then admin tabs
    return [...commonTabs, ...adminTabs];
  }
  // Non-admins only see common tabs
  return commonTabs;
};

export default function DashboardTabs({
  // users, // REMOVED
  isAdmin, // Changed from currentUserIsAdmin
  loggedInUserId,
  // subscription, // REMOVE old prop
  // productName, // REMOVE old prop
  subscriptions, // NEW prop
  products, // NEW prop
  purchases = null, // Add one-time purchases with default value
}: DashboardTabsProps) {
  const initialAvailableTabs = getAvailableTabs(isAdmin);
  const [activeTab, setActiveTab] = useState<TabName>("Subscription");
  const [tabs, setTabs] = useState<TabName[]>(initialAvailableTabs);
  const [refsReady, setRefsReady] = useState(false);

  // Refs for floating indicator
  const rootRef = useRef<HTMLDivElement | null>(null);
  const controlsRefs = useRef<Record<string, HTMLButtonElement | null>>({});
  
  const setControlRef = useCallback((val: string) => (node: HTMLButtonElement | null) => {
    controlsRefs.current[val] = node;
  }, []);

  // Trigger a re-render once refs have been assigned after first mount
  useEffect(() => {
    setRefsReady(true);
  }, []);

  useEffect(() => {
    const availableTabs = getAvailableTabs(isAdmin);
    setTabs(availableTabs);
    if (!availableTabs.includes(activeTab)) {
      setActiveTab(availableTabs[0]);
    }
  }, [isAdmin, activeTab]);

  return (
    <div className="w-full max-w-6xl mx-auto pt-6 relative">
      <Tabs value={activeTab} onChange={(value) => setActiveTab(value as TabName || "Subscription")} variant="none">
        <Tabs.List ref={rootRef} className="mb-6">
          {tabs.map((tab) => (
            <Tabs.Tab 
              key={tab}
              value={tab} 
              ref={setControlRef(tab)}
              className="px-4 py-2 font-medium"
            >
              {tab}
            </Tabs.Tab>
          ))}

          <FloatingIndicator
            target={refsReady && activeTab ? controlsRefs.current[activeTab] : null}
            parent={rootRef.current}
          />
        </Tabs.List>

        {tabs.map((tab) => (
          <Tabs.Panel key={tab} value={tab} className="p-6">
            <Suspense fallback={<div className="flex justify-center items-center h-full"><span className="loading loading-spinner loading-lg"></span></div>}>
              {tab === "Subscription" && <SubscriptionManagement subscriptions={subscriptions} products={products} purchases={purchases} />}
              {tab === "Product Management" && isAdmin && <ProductManagement />}
              {tab === "User Management" && isAdmin && <UserManagement />}
              {tab === "Change Password" && <ChangePasswordForm />}
              {tab === "Delete Account" && <DeleteAccountButton />}
            </Suspense>
          </Tabs.Panel>
        ))}
      </Tabs>
      
      {/* Admin badge overlay for admin tabs */}
      {isAdmin && (activeTab === "User Management" || activeTab === "Product Management") && (
        <div className="absolute top-2 right-2">
          <span className="text-xs font-medium uppercase badge badge-primary badge-outline">Admin</span>
        </div>
      )}
    </div>
  );
} 