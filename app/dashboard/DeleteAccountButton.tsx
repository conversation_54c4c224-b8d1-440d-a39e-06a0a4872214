'use client';

import { useState, useTransition } from 'react';
import { useRouter } from 'next/navigation';
import { deleteCurrentUserAccount } from './actions';
import { createClient } from '@/lib/supabase/client';
import Modal from '@/app/components/ui/Modal';

export default function DeleteAccountButton() {
  const router = useRouter();
  const supabase = createClient();
  const [message, setMessage] = useState<{ text: string; type: 'success' | 'error' } | null>(null);
  const [isPending, startTransition] = useTransition();
  const [isModalOpen, setIsModalOpen] = useState(false);

  const confirmDelete = () => {
    setIsModalOpen(false);
    setMessage(null);

    startTransition(async () => {
      const result = await deleteCurrentUserAccount();
      if (result.success) {
        setMessage({ text: result.message, type: 'success' });
        await supabase.auth.signOut();
        router.push('/?message=Account deleted successfully&status=success');
        router.refresh();
      } else {
        setMessage({ text: result.message || 'An unexpected error occurred.', type: 'error' });
      }
    });
  };

  const openConfirmModal = () => {
    setMessage(null);
    setIsModalOpen(true);
  };

  const cancelDelete = () => {
    setIsModalOpen(false);
  };

  return (
    <div>
      <h2 className="mb-[.5em]">Delete Account</h2>
      <p className=" text-base-content/70 mb-4">
        Permanently delete your account and all associated data. This action is irreversible.
      </p>
      <button
        onClick={openConfirmModal}
        disabled={isPending}
        className="btn btn-error w-full"
      >
        {isPending ? <><span className="loading loading-spinner"></span>Deleting Account...</> : 'Delete My Account'}
      </button>
      {message && (
        <div
          role="alert"
          className={`alert mt-4  ${
            message.type === 'success' ? 'alert-success' : 'alert-error'
          }`}
        >
          <span>{message.text}</span>
        </div>
      )}

      <Modal 
        id="delete-account-modal"
        isOpen={isModalOpen} 
        onClose={cancelDelete} 
        title="Confirm Account Deletion"
      >
        <p className="mb-4">
          Are you sure you want to delete your account? This action cannot be undone. 
          All your data associated with this account will be permanently removed.
        </p>
        <div className="flex justify-end space-x-2 mt-4">
          <button onClick={cancelDelete} className="btn btn-ghost">
            Cancel
          </button>
          <button onClick={confirmDelete} className="btn btn-error" disabled={isPending}>
            {isPending ? <><span className="loading loading-spinner"></span>Deleting...</> : 'Confirm Delete'}
          </button>
        </div>
      </Modal>
    </div>
  );
} 
