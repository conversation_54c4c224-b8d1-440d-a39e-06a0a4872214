import Link from 'next/link';
import { useRouter } from 'next/navigation';
import ProductList from '../components/products/ProductList';
import { Product } from '@/app/utils/pricingUtils';

interface Subscription {
  stripe_subscription_id: string;
  status: string;
  stripe_product_id: string | null;
  product_name?: string;
  current_period_end: string;
  cancel_at_period_end: boolean;
  user_id?: string;
}

interface OneTimePurchase {
  stripe_product_id: string;
  product_name?: string;
}

interface SubscriptionManagementProps {
  subscriptions: Subscription[] | null;
  products: Product[];
  purchases?: OneTimePurchase[] | null;
}

export default function SubscriptionManagement({ subscriptions, products, purchases }: SubscriptionManagementProps) {
  return (
    <div>
      <h2 className="-mb-[.5em]">My Subscriptions</h2>
      
      <div className="mb-8">
        <ProductList 
          listType="subscriptions"
          displayMode="upgrade"
          showCancelButton={true}
          subscriptions={subscriptions}
          products={products}
          gridColumns={3}
        />
      </div>

      <div className="mt-8">
        <h2>Add-ons</h2>
        <ProductList 
          listType="addons"
          displayMode="current"
          purchases={purchases}
          products={products}
          gridColumns={3}
        />
      </div>

      <div className="mt-12 pt-4 border-t">
        <h3 className="text-xl font-semibold text-primary-700 mb-4">Available Plans</h3>
        <div className="bg-secondary-50 p-4 rounded-lg">
          <ProductList 
            listType="subscriptions"
            displayMode="all"
            subscriptions={subscriptions}
            products={products}
            gridColumns={3}
          />
        </div>
      </div>
    </div>
  );
} 
