import { redirect } from 'next/navigation';
import { createClient, createAdminClient } from '@/lib/supabase/server';
import { initializeSupabaseAdmin } from '@/lib/supabase/admin';
import ChangePasswordForm from './ChangePasswordForm';
import DeleteAccountButton from './DeleteAccountButton';
import DashboardTabs from './DashboardTabs';
import Strip<PERSON> from 'stripe';

if (!process.env.STRIPE_SECRET_KEY && process.env.NODE_ENV !== 'test') { // Added NODE_ENV check for tests
  // throw new Error('STRIPE_SECRET_KEY is not set in environment variables'); // Temporarily allow missing for build
  console.warn('STRIPE_SECRET_KEY is not set in environment variables. Stripe functionality will be limited.');
}

const stripe = process.env.STRIPE_SECRET_KEY 
  ? new Stripe(process.env.STRIPE_SECRET_KEY, { apiVersion: '2025-04-30.basil' })
  : null; 


// Define types for data passed to SubscriptionManagement
interface ManagedSubscription {
  stripe_subscription_id: string;
  status: string;
  stripe_product_id: string | null; // from profile or Stripe
  product_name?: string; // To be fetched
  current_period_end: string; // from profile or Stripe, formatted
  cancel_at_period_end: boolean; // Fetched from Stripe
  // user_id: string; // Not strictly needed by SubscriptionManagement if data is already for the logged-in user
}

interface ManagedProduct { // Renamed from Product to avoid conflict if importing Stripe.Product
  stripe_product_id: string;
  name: string;
}

interface ManagedOneTimePurchase {
  // id: string; // Raw profiles.purchases is an array of product_ids, not objects with their own id
  stripe_product_id: string;
  product_name?: string; // To be fetched
  // stripe_price_id: string; // Not in profiles.purchases
  // stripe_payment_intent_id: string; // Not in profiles.purchases
  purchase_date?: string; // Not directly in profiles.purchases, we'd need to infer or omit
}


// --- Types for existing admin user table (can be refactored/removed if admin part changes) ---
// User, UserSubscription, SupabaseAuthUser, RawSubscriptionData interfaces were here and are now removed.
// --- End types for admin user table ---


// getUsers function was here and is now removed.


export default async function DashboardPage() {
  const supabase = await createClient();
  const supabaseAdminClient = await createAdminClient(); // For auth users list if admin
  const directAdmin = initializeSupabaseAdmin(); // For direct db access (profiles) & FDWs if still used by getUsers

  const { data: { user: loggedInUser }, error: userError } = await supabase.auth.getUser();

  if (userError || !loggedInUser) {
    redirect('/auth?message=Please log in to view this page');
  }

  let managedSubscription: ManagedSubscription | null = null;
  let oneTimePurchases: ManagedOneTimePurchase[] = [];
  const productDetailsMap = new Map<string, ManagedProduct>();

  // Fetch from public.profiles for the loggedInUser
  console.log(`Fetching profile for logged-in user: ${loggedInUser.id}`);
  const { data: profile, error: profileError } = await directAdmin
    .from('profiles')
    .select('*')
    .eq('id', loggedInUser.id)
    .single();

  if (profileError && profileError.code !== 'PGRST116') { // PGRST116 = 0 rows
    console.error('Error fetching user profile:', profileError.message);
    // Potentially show an error or allow limited functionality
  }

  const productIdsToFetchFromMeta = new Set<string>();

  if (profile) {
    console.log('Profile found:', {
      stripe_subscription_id: profile.stripe_subscription_id,
      status: profile.subscription_status,
      product: profile.subscribed_product_id
    });
    // Attempt to fetch active/relevant subscription details
    if (profile.stripe_subscription_id && profile.subscription_status) {
      let cancelAtPeriodEnd = false;
      let currentPeriodEnd = profile.subscription_current_period_end 
        ? new Date(profile.subscription_current_period_end).toISOString()
        : new Date().toISOString(); // Fallback, should come from Stripe if possible

      if (stripe) { // Check if Stripe client is initialized
          try {
            console.log(`Fetching Stripe subscription ${profile.stripe_subscription_id} for cancel_at_period_end`);
            const stripeSub = await stripe.subscriptions.retrieve(profile.stripe_subscription_id);
            cancelAtPeriodEnd = (stripeSub as any).cancel_at_period_end;
            
            const stripeCurrentPeriodEnd = (stripeSub as any).current_period_end;
            if (typeof stripeCurrentPeriodEnd === 'number' && stripeCurrentPeriodEnd > 0) {
              currentPeriodEnd = new Date(stripeCurrentPeriodEnd * 1000).toISOString();
            } else {
              console.warn(`Stripe subscription ${profile.stripe_subscription_id} has invalid current_period_end: ${stripeCurrentPeriodEnd}. Using profile data or fallback.`);
              // currentPeriodEnd remains as initialized from profile or default
            }

            const status = stripeSub.status;

             // Ensure product ID from profile is a string before adding to set
             if (profile.subscribed_product_id && typeof profile.subscribed_product_id === 'string') {
              productIdsToFetchFromMeta.add(profile.subscribed_product_id);
            } else if (stripeSub.items.data.length > 0 && stripeSub.items.data[0].price && stripeSub.items.data[0].price.product && typeof stripeSub.items.data[0].price.product === 'string') {
              // Fallback to product ID from Stripe subscription if profile data is missing/invalid
              console.warn(`Using product ID from Stripe subscription item for user ${loggedInUser.id} as profile.subscribed_product_id was not a valid string.`);
              productIdsToFetchFromMeta.add(stripeSub.items.data[0].price.product);
            }


            managedSubscription = {
              stripe_subscription_id: profile.stripe_subscription_id,
              status: status,
              stripe_product_id: typeof profile.subscribed_product_id === 'string' ? profile.subscribed_product_id : (stripeSub.items.data.length > 0 && stripeSub.items.data[0].price && typeof stripeSub.items.data[0].price.product === 'string' ? stripeSub.items.data[0].price.product : null),
              // productName will be populated later
              current_period_end: currentPeriodEnd, // Use the potentially Stripe-updated value
              cancel_at_period_end: cancelAtPeriodEnd,
            };

          } catch (e: any) {
            console.error(`Error fetching Stripe subscription ${profile.stripe_subscription_id} directly for user ${loggedInUser.id}: ${e.message}`);
            // Fallback to profile data if Stripe call fails
            managedSubscription = {
              stripe_subscription_id: profile.stripe_subscription_id,
              status: profile.subscription_status,
              stripe_product_id: typeof profile.subscribed_product_id === 'string' ? profile.subscribed_product_id : null,
              current_period_end: profile.subscription_current_period_end 
                ? new Date(profile.subscription_current_period_end).toISOString() 
                : new Date().toISOString(), // Fallback to profile or current date
              cancel_at_period_end: false, // Cannot determine from Stripe if call failed
            };
            if (profile.subscribed_product_id && typeof profile.subscribed_product_id === 'string') {
                productIdsToFetchFromMeta.add(profile.subscribed_product_id);
            }
          }
      } else { // Stripe client not available, use profile data only
        console.warn("Stripe client not initialized. Subscription details from profile might be stale.");
        managedSubscription = {
          stripe_subscription_id: profile.stripe_subscription_id,
          status: profile.subscription_status,
          stripe_product_id: typeof profile.subscribed_product_id === 'string' ? profile.subscribed_product_id : null,
          current_period_end: profile.subscription_current_period_end 
            ? new Date(profile.subscription_current_period_end).toISOString() 
            : new Date().toISOString(),
          cancel_at_period_end: false, // Cannot determine
        };
         if (profile.subscribed_product_id && typeof profile.subscribed_product_id === 'string') {
            productIdsToFetchFromMeta.add(profile.subscribed_product_id);
        }
      }
    }

    // Process one-time purchases from profile.purchases
    if (profile.purchases && Array.isArray(profile.purchases)) {
      profile.purchases.forEach((productId: string) => { // Assuming purchases is an array of product IDs
        if (typeof productId === 'string') {
          productIdsToFetchFromMeta.add(productId);
          oneTimePurchases.push({
            stripe_product_id: productId,
            // product_name will be populated later
          });
        } else {
          console.warn('Encountered non-string item in profile.purchases:', productId);
        }
      });
    }
  } else {
    console.log(`No profile found for user ${loggedInUser.id}. User might need to complete signup or profile creation.`);
  }

  // Fetch product names for all unique product IDs gathered
  if (productIdsToFetchFromMeta.size > 0 && stripe) {
    console.log('Fetching product names for IDs:', Array.from(productIdsToFetchFromMeta));
    try {
      for (const productId of productIdsToFetchFromMeta) {
        // Check cache first (though in this server component context, direct fetch per request is okay)
        if (!productDetailsMap.has(productId)) {
          const product = await stripe.products.retrieve(productId);
          productDetailsMap.set(productId, { stripe_product_id: product.id, name: product.name });
        }
      }

      // Populate product names in managedSubscription and oneTimePurchases
      if (managedSubscription && managedSubscription.stripe_product_id) {
        const productDetail = productDetailsMap.get(managedSubscription.stripe_product_id);
        if (productDetail) {
          managedSubscription.product_name = productDetail.name;
        } else {
          console.warn(`Product name for subscription product ID ${managedSubscription.stripe_product_id} not found after fetch.`);
           managedSubscription.product_name = 'Unknown Product (Name fetch failed)';
        }
      }
      oneTimePurchases = oneTimePurchases.map(purchase => {
        const productDetail = productDetailsMap.get(purchase.stripe_product_id);
        return {
          ...purchase,
          product_name: productDetail ? productDetail.name : 'Unknown Product (Name fetch failed)',
        };
      });

    } catch (error: any) {
      console.error('Error fetching product details from Stripe:', error.message);
      // Populate with placeholders if fetching names fails
      if (managedSubscription && managedSubscription.stripe_product_id && !managedSubscription.product_name) {
        managedSubscription.product_name = 'Error: Product Name Unavailable';
      }
      oneTimePurchases = oneTimePurchases.map(purchase => ({
        ...purchase,
        product_name: purchase.product_name || 'Error: Product Name Unavailable',
      }));
    }
  } else if (productIdsToFetchFromMeta.size > 0 && !stripe) {
      console.warn("Stripe client not initialized. Cannot fetch product names.");
      // Populate with placeholders if Stripe client is not available
      if (managedSubscription && managedSubscription.stripe_product_id && !managedSubscription.product_name) {
        managedSubscription.product_name = 'Product Name N/A (Stripe not init)';
      }
      oneTimePurchases = oneTimePurchases.map(purchase => ({
        ...purchase,
        product_name: purchase.product_name || 'Product Name N/A (Stripe not init)',
      }));
  }

  // Format current_period_end for display *after* all data fetching
  if (managedSubscription && managedSubscription.current_period_end) {
    try {
      // Validate if it's a valid ISO string or timestamp before formatting
      const date = new Date(managedSubscription.current_period_end);
      if (!isNaN(date.getTime())) {
        managedSubscription.current_period_end = date.toLocaleDateString();
      } else {
        console.warn(`Invalid date for current_period_end before formatting: ${managedSubscription.current_period_end}`);
        managedSubscription.current_period_end = 'Invalid Date';
      }
    } catch (e) {
      console.warn(`Error formatting current_period_end: ${managedSubscription.current_period_end}`);
      managedSubscription.current_period_end = 'Invalid Date';
    }
  }


  // Admin specific data fetching (User table) - This is now handled by UserManagement.tsx itself
  // const isAdmin = loggedInUser?.app_metadata?.is_admin === true;
  // No longer fetching adminUsers here. UserManagement.tsx fetches its own data.

  // Determine if the current user is an admin for conditional rendering in DashboardTabs
  const isAdmin = loggedInUser?.app_metadata?.is_admin === true;

  return (
    <div className="dashboard w-full min-h-screen py-8 px-4 md:px-8 lg:px-16">
      <div className="max-w-6xl mx-auto">
        <h1>Settings</h1>
        <p className="text-gray-600 dark:text-gray-300 mb-2">Welcome back, {loggedInUser.user_metadata?.name || loggedInUser.email}</p>
        
        <DashboardTabs
          isAdmin={isAdmin} // Pass admin status
          loggedInUserId={loggedInUser.id} // Pass loggedInUserId
          subscriptions={managedSubscription ? [managedSubscription] : []} // Pass logged-in user's subscription
          products={Array.from(productDetailsMap.values())} // Pass fetched product details
          purchases={oneTimePurchases} // Pass logged-in user's one-time purchases
          // adminUsers prop removed
        />
      </div>
    </div>
  );
} 