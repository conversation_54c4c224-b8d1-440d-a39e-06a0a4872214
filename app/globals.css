@import "tailwindcss";
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: var(--color-base-100);
  --foreground: #171717;
  --font-kantumruy-pro: "Kantumruy Pro";
  
  /* Base color hues */
  --base-hue: 220;      /* Subtle blue undertone */
  --primary-hue: 250;   /* Deep purple */
  --secondary-hue: 190; /* Ocean blue */ 
  --accent-hue: 280;    /* Vibrant purple */
  --neutral-hue: 220;   /* Cool gray */
  --info-hue: 200;      /* Sky blue */
  --success-hue: 150;   /* Emerald green */
  --warning-hue: 45;    /* Warm yellow */
  --error-hue: 0;       /* Pure red */
}

@theme{
  --color-error-dark: hsl(var(--error-hue), 50%, 30%);
  --color-borders: hsl(var(--base-hue), 20%, 90%);
  --radius-buttons: 0.5em;
}

@plugin "daisyui/theme" {
  name: "mytheme";
  default: true;
  prefersdark: false;
  color-scheme: light;


  /* Base colors */
  --color-base-100: hsl(var(--base-hue), 20%, 98%);
  --color-base-200: hsl(var(--base-hue), 25%, 95%);
  --color-base-300: hsl(var(--base-hue), 30%, 92%);
  --color-base-content: hsl(var(--base-hue), 25%, 10%);

  /* Primary - Deep purple */
  --color-primary: hsl(var(--primary-hue), 60%, 50%);
  --color-primary-content: hsl(var(--primary-hue), 10%, 98%);

  /* Secondary - Ocean blue */
  --color-secondary: hsl(var(--secondary-hue), 65%, 45%);
  --color-secondary-content: hsl(var(--secondary-hue), 10%, 98%);

  /* Accent - Vibrant purple */
  --color-accent: hsl(var(--accent-hue), 70%, 45%);
  --color-accent-content: hsl(var(--accent-hue), 10%, 98%);

  /* Neutral - Cool gray */
  --color-neutral: hsl(var(--neutral-hue), 15%, 90%);
  --color-neutral-content: hsl(var(--neutral-hue), 25%, 15%);

  /* Info - Sky blue */
  --color-info: hsl(var(--info-hue), 65%, 50%);
  --color-info-content: hsl(var(--info-hue), 10%, 98%);

  /* Success - Emerald green */
  --color-success: hsl(var(--success-hue), 65%, 45%);
  --color-success-content: hsl(var(--success-hue), 10%, 98%);

  /* Warning - Warm yellow */
  --color-warning: hsl(var(--warning-hue), 90%, 80%);
  --color-warning-content: hsl(var(--warning-hue), 50%, 35%);

  /* Error - Pure red */
  --color-error: hsl(var(--error-hue), 75%, 45%);
  --color-error-content: hsl(var(--error-hue), 10%, 98%);

  /* border radius */
  --radius-selector: 1em;
  --radius-field: .5em;
  --radius-box: 1em;
  --tabcontent-radius-ss: var(--radius-box) !important;
  --tabcontent-radius-se: var(--radius-box) !important;
  --tabcontent-radius-es: var(--radius-box) !important;
  --tabcontent-radius-ee: var(--radius-box) !important;


  /* base sizes */
  --size-selector: 0.25rem;
  --size-field: 0.25rem;

  /* border size */
  --border: 1px;

  /* effects */
  --depth: 1;
  --noise: 0;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans), var(--font-kantumruy-pro);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-kantumruy-pro), Arial, Helvetica, sans-serif;
}

h1 {
  @apply text-[2.5em];
}

h2 {
  @apply text-[2em];
}

h3 {
  @apply text-[1.5em];
}



h1, h2, h3, h4, h5, h6 {
  @apply font-[500];
}

.dashboard h2 {
  @apply !text-xl;
}

table td, table th {
  @apply first:pl-0;
}
