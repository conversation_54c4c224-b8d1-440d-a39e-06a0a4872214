import { UserDataProvider } from "@/contexts/UserDataContext"
import { AuthProvider } from "@/contexts/AuthContext"
import ClientUserNav from "./components/ui/ClientUserNav";
import { MantineProvider, ColorSchemeScript, mantineHtmlProps } from '@mantine/core';
import '@mantine/core/styles.css';
import { theme } from './theme';
import './globals.css';
import { kantumruyPro, atkinsonHyperlegible } from './fonts';

export const metadata = {
  title: 'My Mantine app',
  description: 'I have followed setup instructions carefully',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {

  return (
    <html lang="en" {...mantineHtmlProps}>
      <head>
        <ColorSchemeScript />
      </head>
      <body className={`${kantumruyPro.variable} ${atkinsonHyperlegible.variable}`}>
        <MantineProvider theme={theme}>
          <AuthProvider>
            <UserDataProvider>
              <ClientUserNav />
              {children}
            </UserDataProvider>
          </AuthProvider>
        </MantineProvider>
      </body>
    </html>
  );
}
