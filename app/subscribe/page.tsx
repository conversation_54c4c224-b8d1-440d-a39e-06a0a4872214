'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useSearchParams } from 'next/navigation';
import ProductList from '@/app/components/products/ProductList';
import { createClient } from '@/lib/supabase/client';
import SuccessAlert from '@/app/components/ui/SuccessAlert';

export default function SubscribePage() {
  const [pageError, setPageError] = useState<string | null>(null);
  const searchParams = useSearchParams();
  const priceId = searchParams.get('priceId');
  const productId = searchParams.get('productId');
  
  const supabase = createClient();

  // Callback for ProductList to notify page of updates
  const handleProductListUpdate = useCallback(() => {
    setPageError(null);
    console.log("SubscribePage: ProductList reported an update.");
  }, [setPageError]);

  // Reference to the ProductList component
  const productListRef = React.useRef<any>(null);

  // Check if user is logged in and has priceId/productId params
  useEffect(() => {
    const checkAuthAndInitiateCheckout = async () => {
      if (priceId && productId) {
        const { data } = await supabase.auth.getSession();
        if (data.session && productListRef.current?.handleSubscribeClick) {
          // User is logged in and we have the necessary parameters
          // Small delay to ensure component is fully mounted
          setTimeout(() => {
            productListRef.current.handleSubscribeClick(priceId, productId);
          }, 500);
        }
      }
    };
    
    checkAuthAndInitiateCheckout();
  }, [priceId, productId]);

  return (
    <div className="container mx-auto pt-18">
      {/* Use SuccessAlert component which reads from URL params */}
      <SuccessAlert />
      
      {pageError && (
        <div className="mb-4 p-3 bg-red-100 text-red-700 border border-red-400 rounded text-center">
          Page Error: {pageError} 
        </div>
      )}

      <div className="mb-12">
        <h2 className="border-b-1 border-base-300 pb-2 mb-6">Subscription Plans</h2>
        <ProductList 
            ref={productListRef}
            listType="subscriptions"
            displayMode="all"
            upgradeNumber={1}
            showCancelButton={false}
            onSubscriptionChange={handleProductListUpdate}
            gridColumns={3}
        />
      </div>

      <div>
        <h2 className="border-b-1 border-base-300 pb-2 mb-6">One-Time Add-Ons</h2>
        <ProductList 
            listType="addons"
            displayMode="all"
            gridColumns={3}
        />
      </div>
    </div>
  );
} 
