"use client";

import { Button, Input, Tabs, TextInput, PasswordInput, FloatingIndicator, createTheme, Card, Switch, Modal } from '@mantine/core';
import inputClasses from './theme/Form.module.css';
import buttonClasses from './theme/Buttons.module.css';
import tabClasses from './theme/Tabs.module.css';
import paperClasses from './theme/Paper.module.css';
import cardClasses from './theme/Card.module.css';
import toggleClasses from './theme/Toggle.module.css';
import modalClasses from './theme/Modal.module.css';

const baseHue = 174; // Define your hue variable here

export const theme = createTheme({
  fontFamily: 'var(--font-kantumruy-pro)',
  autoContrast: true,

  components: {
    Input: Input.extend({ classNames: inputClasses }),
    TextInput: TextInput.extend({ classNames: inputClasses }),
    PasswordInput: PasswordInput.extend({ classNames: inputClasses }),
    Button: Button.extend({ 
      classNames: {
        root: buttonClasses.button,
        inner: buttonClasses.inner,
        label: buttonClasses.label,
        loader: buttonClasses.loader
      } 
    }),
    Tabs: Tabs.extend({
      classNames: {
        root: tabClasses.root,
        list: tabClasses.list,
        tab: tabClasses.tab,
        panel: tabClasses.panel
      }
    }),
    FloatingIndicator: FloatingIndicator.extend({
      classNames: {
        root: tabClasses.indicator
      }
    }),
    Card: Card.extend({
      classNames: {
        root: cardClasses.card
      }
    }),
    Switch: Switch.extend({
      classNames: {
        root: toggleClasses.root,
        track: toggleClasses.track,
        thumb: toggleClasses.thumb,
        input: toggleClasses.input,
        label: toggleClasses.label
      }
    }),
    Modal: Modal.extend({
      classNames: {
        root: modalClasses.root,
        header: modalClasses.header,
        title: modalClasses.title,
        body: modalClasses.body,
        close: modalClasses.close,
        overlay: modalClasses.overlay,
        content: modalClasses.content
      }
    }),
  },

  primaryColor: 'blue',
  fontSizes: {
    xs: '0.875em',   // 14px
    sm: '0.9375em',  // 16px
    md: '1em',      // 18px
    lg: '1.25em',  // 20px
    xl: '1.375em',   // 22px
  },
  radius: {
    xs: '2px',
    sm: '4px',
    md: '8px',
    lg: '12px',
    xl: '20px',
    xxl: '24px',
    xxxl: '32px',
  },
  colors: {
    // You can customize your color palette here
    blue: [
      '#e6f7ff',
      '#bae7ff',
      '#91d5ff',
      '#69c0ff',
      '#40a9ff',
      '#1890ff',
      '#096dd9',
      '#0050b3',
      '#003a8c',
      '#002766',
    ],
    borders: [
      `hsl(${baseHue}, 0%, 99%)`,
      `hsl(${baseHue}, 0%, 90%)`,
      `hsl(${baseHue}, 0%, 80%)`,
      `hsl(${baseHue}, 0%, 75%)`,
      `hsl(${baseHue}, 0%, 70%)`,
      `hsl(${baseHue}, 0%, 65%)`,
      `hsl(${baseHue}, 0%, 60%)`,
      `hsl(${baseHue}, 0%, 55%)`,
      `hsl(${baseHue}, 0%, 50%)`,
      `hsl(${baseHue}, 0%, 40%)`,
    ],
  },
});

export const formStyles = { /* your form styles */ };
export const buttonStyles = { /* your button styles */ };
export const tabStyles = { 
  hidden: 'opacity-0 pointer-events-none',
  visible: 'opacity-1 pointer-events-auto'
};

