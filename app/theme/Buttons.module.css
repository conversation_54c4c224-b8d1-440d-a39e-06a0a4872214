.button {
  border-radius: var(--mantine-radius-lg);
  transition: color 100ms ease, background-color 100ms ease;
  outline-offset: -4px;
  height: 2.75em;
  opacity: 1;
  position: relative;
  z-index: 0;
  padding: 0.5em 1em;
  box-shadow: 0px 1px 0px 0 var(--mantine-color-gray-0);
  .label {
    font-weight: 500;
  }
}

.button:active { 
  transform: translateY(0px);
}

.button:focus {
  outline: 5px solid var(--mantine-color-black );
  outline-offset: -5px;
}

/* White variant */
.button[custom-color="white"] {
  background: linear-gradient(to bottom, var(--mantine-color-gray-0), var(--mantine-color-gray-1)), var(--mantine-color-gray-1);
  border: 1px solid var(--mantine-color-gray-3);
  color: var(--mantine-color-gray-7);
}

.button[custom-color="white"]:not([disabled]):hover {
  background: linear-gradient(to bottom, var(--mantine-color-gray-9), var(--mantine-color-black));
  color: var(--mantine-color-white);
  border: 1px solid var(--mantine-color-black);
}

.button[custom-color="black"] {
  background: linear-gradient(to bottom, var(--mantine-color-gray-9), var(--mantine-color-black)), var(--mantine-color-black);
  border: 1px solid var(--mantine-color-gray-3);
  color: var(--mantine-color-white);
}

.button[custom-color="black"]:not([disabled]):hover {
  background: linear-gradient(to bottom, var(--mantine-color-gray-0), var(--mantine-color-white));
  color: var(--mantine-color-black);
  outline: 5px solid var(--mantine-color-black);
  outline-offset: -2px;
}

.button[custom-color="success"]:not([active]):focus {
  outline: 5px solid var(--mantine-color-black );
}

.button[custom-color="success-disabled"] {
  background: hsl(var(--su) / var(--tw-bg-opacity, 0.7));
  border: 1px solid hsl(var(--su) / var(--tw-bg-opacity, 0.7));
  color: hsl(var(--suc) / var(--tw-text-opacity, 0.7));
  cursor: not-allowed;
}


/* Success variant */
.button[custom-color="success"] {
  background: linear-gradient(to bottom, var(--mantine-color-green-6), var(--mantine-color-green-7));
  border: 1px solid var(--mantine-color-green-8);
}

.button[custom-color="success"]:hover {
  background: linear-gradient(to bottom, var(--mantine-color-green-7), var(--mantine-color-green-8));
}

.button[custom-color="success"]:focus {
  outline: 5ch solid var(--mantine-color-black );
}

.button[custom-color="success-disabled"] {
  background: hsl(var(--su) / var(--tw-bg-opacity, 0.7));
  border: 1px solid hsl(var(--su) / var(--tw-bg-opacity, 0.7));
  color: hsl(var(--suc) / var(--tw-text-opacity, 0.7));
  cursor: not-allowed;
}

/* Disabled state */
.button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Link Variant */
.button[data-variant="link"] {
  background: transparent;
  border: none;
  padding: 0;
  height: auto;
  box-shadow: none;
  border-radius: var(--mantine-radius-xs);
}

.button[data-variant="link"]:focus {
  outline: 2px solid var(--mantine-color-black) !important; 
}

.button[data-variant="link"] .label {
  font-weight: normal;
  color: var(--mantine-color-black);
  font-size: var(--mantine-font-size-sm);
}
