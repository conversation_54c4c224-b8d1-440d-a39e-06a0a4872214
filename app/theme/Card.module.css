.card {
  border-radius: var(--mantine-radius-xxl);
  border: 1px solid var(--mantine-color-gray-3);
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
  transition: all 200ms ease;
  background-color: var(--mantine-color-white);
}

.card:hover {
  box-shadow: 0 5px 15px 0 rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

/* Elevated variant */
.card[data-variant="elevated"] {
  border: none;
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.15);
}

.card[data-variant="elevated"]:hover {
  box-shadow: 0 8px 24px 0 rgba(0, 0, 0, 0.2);
}

/* Outline variant */
.card[data-variant="outline"] {
  box-shadow: none;
  border: 2px solid var(--mantine-color-gray-3);
}

.card[data-variant="outline"]:hover {
  border-color: var(--mantine-color-gray-5);
  transform: translateY(-2px);
}

/* Flat variant */
.card[data-variant="flat"] {
  box-shadow: none;
  border: none;
  background-color: var(--mantine-color-gray-0);
}

.card[data-variant="flat"]:hover {
  background-color: var(--mantine-color-gray-1);
}

/* Disabled state */
.card[data-disabled] {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

