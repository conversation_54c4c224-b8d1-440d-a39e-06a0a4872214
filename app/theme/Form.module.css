.root {
  margin-top: var(--mantine-spacing-xs);
}

.input {
  height: 46px;
  padding: 0 var(--mantine-spacing-sm);
  border-radius: var(--mantine-radius-xl);
  border: 1px solid var(--mantine-color-gray-5);
  background-color: var(--mantine-color-gray-0);
  font-size: var(--mantine-font-size-sm);
  transition: border-color 100ms ease;
}

.input:focus-within {
  outline: 5px solid var(--mantine-color-black);
  outline-offset: -2px;
  border: 2px solid var(--mantine-color-white);
}

.label {
  font-weight: 500;
  margin-bottom: 4px;
  color: var(--mantine-color-gray-7);
}

/* Style for disabled state */
.input[data-disabled] {
  background-color: var(--mantine-color-gray-1);
  cursor: not-allowed;
}

.input::placeholder {
  font-size: inherit;
  color: var(--mantine-color-gray-5);
}
