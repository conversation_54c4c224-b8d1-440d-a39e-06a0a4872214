.root {
  background-color: var(--mantine-color-white);
  border-radius: var(--mantine-radius-xl);
  border: 1px solid var(--mantine-color-gray-3);
  padding: 1em;
}

.list {
    position: relative;
    margin-bottom: var(--mantine-spacing-md);
    gap: .5em;
    border-radius: var(--mantine-radius-xl);
  }
  
  .indicator {
    background-color: var(--mantine-color-black);
    border-radius: var(--mantine-radius-xl);  
    @mixin dark {
      background-color: var(--mantine-color-dark-6);
      border-color: var(--mantine-color-dark-4);
    }
  }
  
  .tab {
    z-index: 1;
    font-weight: 400;
    transition: color 100ms ease;
    color: var(--mantine-color-gray-7);
    padding: 1em 1em;
    border-radius: var(--mantine-radius-xl);
    border: 1px solid var(--mantine-color-gray-3);
  
    &[data-active] {
      color: var(--mantine-color-white);
      font-weight: 700 !important;
      border: none;
    }
  
    @mixin dark {
      color: var(--mantine-color-dark-1);
  
      &[data-active] {
        color: var(--mantine-color-white);
      }
    }
  }

  .hidden {
    opacity: 0;
    pointer-events: none;
  }

  .visible {
    opacity: 1;
    pointer-events: auto;
  }

  .panel { 
    background-color: var(--mantine-color-white);
    border-radius: var(--mantine-radius-xl);
  }