
.track {
    background-color: var(--mantine-color-black);
    transition:
      background-color 200ms ease,
      border-color 200ms ease;

    & > .thumb {
        background-color: var(--mantine-color-white);
  
        &::before {
          display: none;
        }
      }

    input:checked + & > .tracklabel {
        color: var(--mantine-color-gray-7);
    }
  
    input:checked + & {
      background-color: var(--mantine-color-green-5);
      border-color: var(--mantine-color-lime-5);

 
      & > .thumb {
        background-color: var(--mantine-color-white);
        box-shadow: 5px 0px 2px 1px var(--mantine-color-green-6);
  
        &::before {
          display: none;
        }
      }
    }
}
