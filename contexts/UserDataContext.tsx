'use client';

import React, { createContext, useState, useContext, ReactNode, useEffect, useCallback } from 'react';
import { createClient } from '@/lib/supabase/client';
import { User } from '@supabase/supabase-js';

interface UserProfile {
  id: string;
  email: string | null;
  stripe_customer_id: string | null;
  stripe_subscription_id: string | null;
  subscription_status: string | null;
  subscribed_product_id: string | null;
  subscription_price_id: string | null;
  subscription_interval: string | null;
  subscription_current_period_end: string | null;
  cancel_at_period_end: boolean | null;
  purchases: string[] | null;
}

interface ActiveSubscription {
  productId: string | null;
  priceId: string | null;
  status: string | null;
  stripeSubscriptionId: string | null;
  cancelAtPeriodEnd: boolean | null;
}

interface UserDataContextType {
  user: User | null;
  userProfile: UserProfile | null;
  activeSubscription: ActiveSubscription | null;
  purchasedProductIds: string[];
  isLoading: boolean;
  isAuthenticating: boolean;
  error: string | null;
  setIsAuthenticating: (isAuthenticating: boolean) => void;
  refreshUserData: () => Promise<void>;
}

const UserDataContext = createContext<UserDataContextType | undefined>(undefined);

export const UserDataProvider = ({ children }: { children: ReactNode }) => {
  const supabase = createClient();
  const [user, setUser] = useState<User | null>(null);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [activeSubscription, setActiveSubscription] = useState<ActiveSubscription | null>(null);
  const [purchasedProductIds, setPurchasedProductIds] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticating, setIsAuthenticating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchUserAndProfile = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Get authenticated user
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      
      if (authError) {
        setError('Could not fetch user session.');
        setUser(null);
        setUserProfile(null);
        setActiveSubscription(null);
        setPurchasedProductIds([]);
        return;
      }
      
      setUser(user);
      
      if (user) {
        // Fetch profile data
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();
          
        if (profileError && profileError.code !== 'PGRST116') {
          setError('Error fetching profile.');
          setUserProfile(null);
        } else if (profile) {
          setUserProfile(profile);
          
          // Set active subscription
          if (profile.stripe_subscription_id && profile.subscription_status) {
            setActiveSubscription({
              productId: profile.subscribed_product_id,
              priceId: profile.subscription_price_id,
              status: profile.subscription_status,
              stripeSubscriptionId: profile.stripe_subscription_id,
              cancelAtPeriodEnd: profile.cancel_at_period_end,
            });
          } else {
            setActiveSubscription(null);
          }
          
          // Set purchased products
          setPurchasedProductIds(profile.purchases && Array.isArray(profile.purchases) ? profile.purchases : []);
        } else {
          setUserProfile(null);
          setActiveSubscription(null);
          setPurchasedProductIds([]);
        }
      } else {
        setUserProfile(null);
        setActiveSubscription(null);
        setPurchasedProductIds([]);
      }
    } catch (err: any) {
      setError(`Unexpected error: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  }, [supabase]);

  // Initial fetch and auth state listener
  useEffect(() => {
    fetchUserAndProfile();
    
    const { data: authListener } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setUser(session?.user ?? null);
        if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
          fetchUserAndProfile();
        } else if (event === 'SIGNED_OUT') {
          setUserProfile(null);
          setActiveSubscription(null);
          setPurchasedProductIds([]);
        }
      }
    );
    
    return () => {
      authListener?.subscription.unsubscribe();
    };
  }, [supabase, fetchUserAndProfile]);

  return (
    <UserDataContext.Provider 
      value={{ 
        user, 
        userProfile, 
        activeSubscription, 
        purchasedProductIds, 
        isLoading, 
        isAuthenticating, 
        error,
        setIsAuthenticating,
        refreshUserData: fetchUserAndProfile
      }}
    >
      {children}
    </UserDataContext.Provider>
  );
};

export const useUserData = () => {
  const context = useContext(UserDataContext);
  if (context === undefined) {
    throw new Error('useUserData must be used within a UserDataProvider');
  }
  return context;
};