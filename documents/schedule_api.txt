Subscription Schedule 
A subscription schedule allows you to create and manage the lifecycle of a subscription by predefining expected changes.

Related guide: Subscription schedules

Endpoints
POST
/v1/subscription_schedules
POST
/v1/subscription_schedules/:id
GET
/v1/subscription_schedules/:id
GET
/v1/subscription_schedules
POST
/v1/subscription_schedules/:id/cancel
POST
/v1/subscription_schedules/:id/release
The Subscription Schedule object 
Attributes

id
string
Unique identifier for the object.


current_phase
nullable object
Object representing the start and end dates for the current phase of the subscription schedule, if it is active.

Hide child attributes

current_phase.end_date
timestamp
The end of this phase of the subscription schedule.


current_phase.start_date
timestamp
The start of this phase of the subscription schedule.


customer
string
Expandable
ID of the customer who owns the subscription schedule.


metadata
nullable object
Set of key-value pairs that you can attach to an object. This can be useful for storing additional information about the object in a structured format.


phases
array of objects
Configuration for the subscription schedule’s phases.

Hide child attributes

phases.add_invoice_items
array of objects
A list of prices and quantities that will generate invoice items appended to the next invoice for this phase.

Show child attributes

phases.application_fee_percent
nullable float
Connect only
A non-negative decimal between 0 and 100, with at most two decimal places. This represents the percentage of the subscription invoice total that will be transferred to the application owner’s Stripe account during this phase of the schedule.


phases.automatic_tax
nullable object
Automatic tax settings for this phase.

Show child attributes

phases.billing_cycle_anchor
nullable enum
Possible values are phase_start or automatic. If phase_start then billing cycle anchor of the subscription is set to the start of the phase when entering the phase. If automatic then the billing cycle anchor is automatically modified as needed when entering the phase. For more information, see the billing cycle documentation.

Possible enum values
automatic
phase_start

phases.billing_thresholds
nullable object
Define thresholds at which an invoice will be sent, and the subscription advanced to a new billing period

Show child attributes

phases.collection_method
nullable enum
Either charge_automatically, or send_invoice. When charging automatically, Stripe will attempt to pay the underlying subscription at the end of each billing cycle using the default source attached to the customer. When sending an invoice, Stripe will email your customer an invoice with payment instructions and mark the subscription as active.

Possible enum values
charge_automatically
send_invoice

phases.currency
enum
Three-letter ISO currency code, in lowercase. Must be a supported currency.


phases.default_payment_method
nullable string
Expandable
ID of the default payment method for the subscription schedule. It must belong to the customer associated with the subscription schedule. If not set, invoices will use the default payment method in the customer’s invoice settings.


phases.default_tax_rates
nullable array of objects
The default tax rates to apply to the subscription during this phase of the subscription schedule.

Hide child attributes

phases.default_tax_rates.id
string
Unique identifier for the object.


phases.default_tax_rates.object
string
String representing the object’s type. Objects of the same type share the same value.


phases.default_tax_rates.active
boolean
Defaults to true. When set to false, this tax rate cannot be used with new applications or Checkout Sessions, but will still work for subscriptions and invoices that already have it set.


phases.default_tax_rates.country
nullable string
Two-letter country code (ISO 3166-1 alpha-2).


phases.default_tax_rates.created
timestamp
Time at which the object was created. Measured in seconds since the Unix epoch.


phases.default_tax_rates.description
nullable string
An arbitrary string attached to the tax rate for your internal use only. It will not be visible to your customers.


phases.default_tax_rates.display_name
string
The display name of the tax rates as it will appear to your customer on their receipt email, PDF, and the hosted invoice page.


phases.default_tax_rates.effective_percentage
nullable float
Actual/effective tax rate percentage out of 100. For tax calculations with automatic_tax[enabled]=true, this percentage reflects the rate actually used to calculate tax based on the product’s taxability and whether the user is registered to collect taxes in the corresponding jurisdiction.


phases.default_tax_rates.flat_amount
nullable object
The amount of the tax rate when the rate_type is flat_amount. Tax rates with rate_type percentage can vary based on the transaction, resulting in this field being null. This field exposes the amount and currency of the flat tax rate.

Hide child attributes

phases.default_tax_rates.flat_amount.amount
integer
Amount of the tax when the rate_type is flat_amount. This positive integer represents how much to charge in the smallest currency unit (e.g., 100 cents to charge $1.00 or 100 to charge ¥100, a zero-decimal currency). The amount value supports up to eight digits (e.g., a value of 99999999 for a USD charge of $999,999.99).


phases.default_tax_rates.flat_amount.currency
string
Three-letter ISO currency code, in lowercase.


phases.default_tax_rates.inclusive
boolean
This specifies if the tax rate is inclusive or exclusive.


phases.default_tax_rates.jurisdiction
nullable string
The jurisdiction for the tax rate. You can use this label field for tax reporting purposes. It also appears on your customer’s invoice.


phases.default_tax_rates.jurisdiction_level
nullable enum
The level of the jurisdiction that imposes this tax rate. Will be null for manually defined tax rates.

Possible enum values
city
country
county
district
multiple
state

phases.default_tax_rates.livemode
boolean
Has the value true if the object exists in live mode or the value false if the object exists in test mode.


phases.default_tax_rates.metadata
nullable object
Set of key-value pairs that you can attach to an object. This can be useful for storing additional information about the object in a structured format.


phases.default_tax_rates.percentage
float
Tax rate percentage out of 100. For tax calculations with automatic_tax[enabled]=true, this percentage includes the statutory tax rate of non-taxable jurisdictions.


phases.default_tax_rates.rate_type
nullable enum
Indicates the type of tax rate applied to the taxable amount. This value can be null when no tax applies to the location. This field is only present for TaxRates created by Stripe Tax.

Possible enum values
flat_amount
A fixed amount applied as tax, regardless of the taxable amount, such as a retail delivery fee.

percentage
A tax rate expressed as a percentage of the taxable amount, such as the sales tax rate in California.


phases.default_tax_rates.state
nullable string
ISO 3166-2 subdivision code, without country prefix. For example, “NY” for New York, United States.


phases.default_tax_rates.tax_type
nullable enum
The high-level tax type, such as vat or sales_tax.

Possible enum values
amusement_tax
Amusement Tax

communications_tax
Communications Tax

gst
Goods and Services Tax

hst
Harmonized Sales Tax

igst
Integrated Goods and Services Tax

jct
Japanese Consumption Tax

lease_tax
Chicago Lease Tax

pst
Provincial Sales Tax

qst
Quebec Sales Tax

retail_delivery_fee
Retail Delivery Fee

Show 4 more

phases.description
nullable string
Subscription description, meant to be displayable to the customer. Use this field to optionally store an explanation of the subscription for rendering in Stripe surfaces and certain local payment methods UIs.


phases.discounts
array of objects
The stackable discounts that will be applied to the subscription on this phase. Subscription item discounts are applied before subscription discounts.

Show child attributes

phases.end_date
timestamp
The end of this phase of the subscription schedule.


phases.invoice_settings
nullable object
The invoice settings applicable during this phase.

Show child attributes

phases.items
array of objects
Subscription items to configure the subscription to during this phase of the subscription schedule.

Show child attributes

phases.metadata
nullable object
Set of key-value pairs that you can attach to a phase. Metadata on a schedule’s phase will update the underlying subscription’s metadata when the phase is entered. Updating the underlying subscription’s metadata directly will not affect the current phase’s metadata.


phases.on_behalf_of
nullable string
Expandable
Connect only
The account (if any) the charge was made on behalf of for charges associated with the schedule’s subscription. See the Connect documentation for details.


phases.proration_behavior
enum
When transitioning phases, controls how prorations are handled (if any). Possible values are create_prorations, none, and always_invoice.

Possible enum values
always_invoice
create_prorations
none

phases.start_date
timestamp
The start of this phase of the subscription schedule.


phases.transfer_data
nullable object
Connect only
The account (if any) the associated subscription’s payments will be attributed to for tax reporting, and where funds from each payment will be transferred to for each of the subscription’s invoices.

Show child attributes

phases.trial_end
nullable timestamp
When the trial ends within the phase.


status
enum
The present status of the subscription schedule. Possible values are not_started, active, completed, released, and canceled. You can read more about the different states in our behavior guide.

Possible enum values
active
canceled
completed
not_started
released

subscription
nullable string
Expandable
ID of the subscription managed by the subscription schedule.

More attributes
Expand all

object
string
String representing the object’s type. Objects of the same type share the same value.


application
nullable string
Expandable
Connect only
ID of the Connect Application that created the schedule.


canceled_at
nullable timestamp
Time at which the subscription schedule was canceled. Measured in seconds since the Unix epoch.


completed_at
nullable timestamp
Time at which the subscription schedule was completed. Measured in seconds since the Unix epoch.


created
timestamp
Time at which the object was created. Measured in seconds since the Unix epoch.


default_settings
object
Object representing the subscription schedule’s default settings.

Show child attributes

end_behavior
enum
Behavior of the subscription schedule and underlying subscription when it ends. Possible values are release or cancel with the default being release. release will end the subscription schedule and keep the underlying subscription running. cancel will end the subscription schedule and cancel the underlying subscription.

Possible enum values
cancel
Cancel the underlying subscription when the subscription schedule ends.

release
Persist the underlying subscription in its current state when the subscription schedule ends.


livemode
boolean
Has the value true if the object exists in live mode or the value false if the object exists in test mode.


released_at
nullable timestamp
Time at which the subscription schedule was released. Measured in seconds since the Unix epoch.


released_subscription
nullable string
ID of the subscription once managed by the subscription schedule (if it is released).


test_clock
nullable string
Expandable
ID of the test clock this subscription schedule belongs to.

Create a schedule 
Creates a new subscription schedule object. Each customer can have up to 500 active or scheduled subscriptions.

Parameters

customer
string
The identifier of the customer to create the subscription schedule for.


metadata
object
Set of key-value pairs that you can attach to an object. This can be useful for storing additional information about the object in a structured format. Individual keys can be unset by posting an empty value to them. All keys can be unset by posting an empty value to metadata.


phases
array of objects
List representing phases of the subscription schedule. Each phase can be customized to have different durations, plans, and coupons. If there are multiple phases, the end_date of one phase will always equal the start_date of the next phase.

Show child parameters

start_date
timestamp | string
When the subscription schedule starts. We recommend using now so that it starts the subscription immediately. You can also use a Unix timestamp to backdate the subscription so that it starts on a past date, or set a future date for the subscription to start on.

More parameters
Expand all

default_settings
object
Object representing the subscription schedule’s default settings.

Hide child parameters

default_settings.application_fee_percent
float
Connect only
A non-negative decimal between 0 and 100, with at most two decimal places. This represents the percentage of the subscription invoice total that will be transferred to the application owner’s Stripe account. The request must be made by a platform account on a connected account in order to set an application fee percentage. For more information, see the application fees documentation.


default_settings.automatic_tax
object
Default settings for automatic tax computation.

Hide child parameters

default_settings.automatic_tax.enabled
boolean
Required
Enabled automatic tax calculation which will automatically compute tax rates on all invoices generated by the subscription.


default_settings.automatic_tax.liability
object
Connect only
The account that’s liable for tax. If set, the business address and tax registrations required to perform the tax calculation are loaded from this account. The tax transaction is returned in the report of the connected account.

Hide child parameters

default_settings.automatic_tax.liability.type
enum
Required
Type of the account referenced in the request.

Possible enum values
account
Indicates that the account being referenced is a connected account which is different from the account making the API request but related to it.

self
Indicates that the account being referenced is the account making the API request.


default_settings.automatic_tax.liability.account
string
Required only if type is account
The connected account being referenced when type is account.


default_settings.billing_cycle_anchor
enum
Can be set to phase_start to set the anchor to the start of the phase or automatic to automatically change it if needed. Cannot be set to phase_start if this phase specifies a trial. For more information, see the billing cycle documentation.

Possible enum values
automatic
phase_start

default_settings.billing_thresholds
object
Define thresholds at which an invoice will be sent, and the subscription advanced to a new billing period. Pass an empty string to remove previously-defined thresholds.

Hide child parameters

default_settings.billing_thresholds.amount_gte
integer
Monetary threshold that triggers the subscription to advance to a new billing period


default_settings.billing_thresholds.reset_billing_cycle_anchor
boolean
Indicates if the billing_cycle_anchor should be reset when a threshold is reached. If true, billing_cycle_anchor will be updated to the date/time the threshold was last reached; otherwise, the value will remain unchanged.


default_settings.collection_method
enum
Either charge_automatically, or send_invoice. When charging automatically, Stripe will attempt to pay the underlying subscription at the end of each billing cycle using the default source attached to the customer. When sending an invoice, Stripe will email your customer an invoice with payment instructions and mark the subscription as active. Defaults to charge_automatically on creation.

Possible enum values
charge_automatically
send_invoice

default_settings.default_payment_method
string
ID of the default payment method for the subscription schedule. It must belong to the customer associated with the subscription schedule. If not set, invoices will use the default payment method in the customer’s invoice settings.


default_settings.description
string
Subscription description, meant to be displayable to the customer. Use this field to optionally store an explanation of the subscription for rendering in Stripe surfaces and certain local payment methods UIs.


default_settings.invoice_settings
object
All invoices will be billed using the specified settings.

Show child parameters

default_settings.on_behalf_of
string
Connect only
The account on behalf of which to charge, for each of the associated subscription’s invoices.


default_settings.transfer_data
object
Connect only
The data with which to automatically create a Transfer for each of the associated subscription’s invoices.

Show child parameters

end_behavior
enum
Behavior of the subscription schedule and underlying subscription when it ends. Possible values are release or cancel with the default being release. release will end the subscription schedule and keep the underlying subscription running. cancel will end the subscription schedule and cancel the underlying subscription.

Possible enum values
cancel
Cancel the underlying subscription when the subscription schedule ends.

release
Persist the underlying subscription in its current state when the subscription schedule ends.


from_subscription
string
Migrate an existing subscription to be managed by a subscription schedule. If this parameter is set, a subscription schedule will be created using the subscription’s item(s), set to auto-renew using the subscription’s interval. When using this parameter, other parameters (such as phase values) cannot be set. To create a subscription schedule with other modifications, we recommend making two separate API calls.

Returns
Returns a subscription schedule object if the call succeeded.

POST 
/v1/subscription_schedules
Server-side language

cURL
curl https://api.stripe.com/v1/subscription_schedules \
  -u "sk_test_51RM6VhD6e5d6MzhOErw0xgWv3b0ikASKaAqR7F99xvXKSBqemvGHSaRItYmzP6bOZpFWjFg7M1xYHHf9QeoA11WX00Q44iCIDB:" \
  -d customer=cus_NcI8FsMbh0OeFs \
  -d start_date=1787130418 \
  -d end_behavior=release \
  -d "phases[0][items][0][price]"=price_1Mr3YcLkdIwHu7ixYCFhXHNb \
  -d "phases[0][items][0][quantity]"=1 \
  -d "phases[0][iterations]"=12
Response
{
  "id": "sub_sched_1Mr3YdLkdIwHu7ixjop3qtff",
  "object": "subscription_schedule",
  "application": null,
  "canceled_at": null,
  "completed_at": null,
  "created": 1724058651,
  "current_phase": null,
  "customer": "cus_NcI8FsMbh0OeFs",
  "default_settings": {
    "application_fee_percent": null,
    "automatic_tax": {
      "enabled": false,
      "liability": null
    },
    "billing_cycle_anchor": "automatic",
    "collection_method": "charge_automatically",
    "default_payment_method": null,
    "default_source": null,
    "description": null,
    "invoice_settings": {
      "issuer": {
        "type": "self"
      }
    },
    "on_behalf_of": null,
    "transfer_data": null
  },
  "end_behavior": "release",
  "livemode": false,
  "metadata": {},
  "phases": [
    {
      "add_invoice_items": [],
      "application_fee_percent": null,
      "billing_cycle_anchor": null,
      "collection_method": null,
      "currency": "usd",
      "default_payment_method": null,
      "default_tax_rates": [],
      "description": null,
      "discounts": null,
      "end_date": 1818666418,
      "invoice_settings": null,
      "items": [
        {
          "discounts": null,
          "metadata": {},
          "plan": "price_1Mr3YcLkdIwHu7ixYCFhXHNb",
          "price": "price_1Mr3YcLkdIwHu7ixYCFhXHNb",
          "quantity": 1,
          "tax_rates": []
        }
      ],
      "metadata": {},
      "on_behalf_of": null,
      "proration_behavior": "create_prorations",
      "start_date": 1787130418,
      "transfer_data": null,
      "trial_end": null
    }
  ],
  "released_at": null,
  "released_subscription": null,
  "renewal_interval": null,
  "status": "not_started",
  "subscription": null,
  "test_clock": null
}
Update a schedule 
Updates an existing subscription schedule.

Parameters

metadata
object
Set of key-value pairs that you can attach to an object. This can be useful for storing additional information about the object in a structured format. Individual keys can be unset by posting an empty value to them. All keys can be unset by posting an empty value to metadata.


phases
array of objects
List representing phases of the subscription schedule. Each phase can be customized to have different durations, plans, and coupons. If there are multiple phases, the end_date of one phase will always equal the start_date of the next phase. Note that past phases can be omitted.

Show child parameters

proration_behavior
enum
If the update changes the billing configuration (item price, quantity, etc.) of the current phase, indicates how prorations from this change should be handled. The default value is create_prorations.

Possible enum values
always_invoice
Prorate changes, and force an invoice to be immediately created for any prorations.

create_prorations
Prorate changes, but leave any prorations as pending invoice items to be picked up on the customer’s next invoice.

none
Does not create any prorations.

More parameters
Expand all

default_settings
object
Object representing the subscription schedule’s default settings.

Hide child parameters

default_settings.application_fee_percent
float
Connect only
A non-negative decimal between 0 and 100, with at most two decimal places. This represents the percentage of the subscription invoice total that will be transferred to the application owner’s Stripe account. The request must be made by a platform account on a connected account in order to set an application fee percentage. For more information, see the application fees documentation.


default_settings.automatic_tax
object
Default settings for automatic tax computation.

Hide child parameters

default_settings.automatic_tax.enabled
boolean
Required
Enabled automatic tax calculation which will automatically compute tax rates on all invoices generated by the subscription.


default_settings.automatic_tax.liability
object
Connect only
The account that’s liable for tax. If set, the business address and tax registrations required to perform the tax calculation are loaded from this account. The tax transaction is returned in the report of the connected account.

Hide child parameters

default_settings.automatic_tax.liability.type
enum
Required
Type of the account referenced in the request.

Possible enum values
account
Indicates that the account being referenced is a connected account which is different from the account making the API request but related to it.

self
Indicates that the account being referenced is the account making the API request.


default_settings.automatic_tax.liability.account
string
Required only if type is account
The connected account being referenced when type is account.


default_settings.billing_cycle_anchor
enum
Can be set to phase_start to set the anchor to the start of the phase or automatic to automatically change it if needed. Cannot be set to phase_start if this phase specifies a trial. For more information, see the billing cycle documentation.

Possible enum values
automatic
phase_start

default_settings.billing_thresholds
object
Define thresholds at which an invoice will be sent, and the subscription advanced to a new billing period. Pass an empty string to remove previously-defined thresholds.

Hide child parameters

default_settings.billing_thresholds.amount_gte
integer
Monetary threshold that triggers the subscription to advance to a new billing period


default_settings.billing_thresholds.reset_billing_cycle_anchor
boolean
Indicates if the billing_cycle_anchor should be reset when a threshold is reached. If true, billing_cycle_anchor will be updated to the date/time the threshold was last reached; otherwise, the value will remain unchanged.


default_settings.collection_method
enum
Either charge_automatically, or send_invoice. When charging automatically, Stripe will attempt to pay the underlying subscription at the end of each billing cycle using the default source attached to the customer. When sending an invoice, Stripe will email your customer an invoice with payment instructions and mark the subscription as active. Defaults to charge_automatically on creation.

Possible enum values
charge_automatically
send_invoice

default_settings.default_payment_method
string
ID of the default payment method for the subscription schedule. It must belong to the customer associated with the subscription schedule. If not set, invoices will use the default payment method in the customer’s invoice settings.


default_settings.description
string
Subscription description, meant to be displayable to the customer. Use this field to optionally store an explanation of the subscription for rendering in Stripe surfaces and certain local payment methods UIs.


default_settings.invoice_settings
object
All invoices will be billed using the specified settings.

Show child parameters

default_settings.on_behalf_of
string
Connect only
The account on behalf of which to charge, for each of the associated subscription’s invoices.


default_settings.transfer_data
object
Connect only
The data with which to automatically create a Transfer for each of the associated subscription’s invoices.

Hide child parameters

default_settings.transfer_data.destination
string
Required
ID of an existing, connected Stripe account.


default_settings.transfer_data.amount_percent
float
A non-negative decimal between 0 and 100, with at most two decimal places. This represents the percentage of the subscription invoice total that will be transferred to the destination account. By default, the entire amount is transferred to the destination.


end_behavior
enum
Behavior of the subscription schedule and underlying subscription when it ends. Possible values are release or cancel with the default being release. release will end the subscription schedule and keep the underlying subscription running. cancel will end the subscription schedule and cancel the underlying subscription.

Possible enum values
cancel
Cancel the underlying subscription when the subscription schedule ends.

release
Persist the underlying subscription in its current state when the subscription schedule ends.

Returns
Returns an updated subscription schedule object if the call succeeded.

POST 
/v1/subscription_schedules/:id
Server-side language

cURL
curl https://api.stripe.com/v1/subscription_schedules/sub_sched_1Mr3YdLkdIwHu7ixjop3qtff \
  -u "sk_test_51RM6VhD6e5d6MzhOErw0xgWv3b0ikASKaAqR7F99xvXKSBqemvGHSaRItYmzP6bOZpFWjFg7M1xYHHf9QeoA11WX00Q44iCIDB:" \
  -d end_behavior=release
Response
{
  "id": "sub_sched_1Mr3YdLkdIwHu7ixjop3qtff",
  "object": "subscription_schedule",
  "application": null,
  "canceled_at": null,
  "completed_at": null,
  "created": 1680113835,
  "current_phase": null,
  "customer": "cus_NcI8FsMbh0OeFs",
  "default_settings": {
    "application_fee_percent": null,
    "automatic_tax": {
      "enabled": false,
      "liability": null
    },
    "billing_cycle_anchor": "automatic",
    "collection_method": "charge_automatically",
    "default_payment_method": null,
    "default_source": null,
    "description": null,
    "invoice_settings": {
      "issuer": {
        "type": "self"
      }
    },
    "on_behalf_of": null,
    "transfer_data": null
  },
  "end_behavior": "release",
  "livemode": false,
  "metadata": {},
  "phases": [
    {
      "add_invoice_items": [],
      "application_fee_percent": null,
      "billing_cycle_anchor": null,
      "collection_method": null,
      "currency": "usd",
      "default_payment_method": null,
      "default_tax_rates": [],
      "description": null,
      "end_date": 1712339228,
      "invoice_settings": null,
      "items": [
        {
          "metadata": {},
          "plan": "price_1Mr3YcLkdIwHu7ixYCFhXHNb",
          "price": "price_1Mr3YcLkdIwHu7ixYCFhXHNb",
          "quantity": 1,
          "tax_rates": []
        }
      ],
      "metadata": {},
      "on_behalf_of": null,
      "proration_behavior": "create_prorations",
      "start_date": 1680716828,
      "transfer_data": null,
      "trial_end": null
    }
  ],
  "released_at": null,
  "released_subscription": null,
  "renewal_interval": null,
  "status": "not_started",
  "subscription": null,
  "test_clock": null
}
Retrieve a schedule 
Retrieves the details of an existing subscription schedule. You only need to supply the unique subscription schedule identifier that was returned upon subscription schedule creation.

Parameters
No parameters.

Returns
Returns a subscription schedule object if a valid identifier was provided.

GET 
/v1/subscription_schedules/:id
Server-side language

cURL
curl https://api.stripe.com/v1/subscription_schedules/sub_sched_1Mr3YdLkdIwHu7ixjop3qtff \
  -u "sk_test_51RM6VhD6e5d6MzhOErw0xgWv3b0ikASKaAqR7F99xvXKSBqemvGHSaRItYmzP6bOZpFWjFg7M1xYHHf9QeoA11WX00Q44iCIDB:"
Response
{
  "id": "sub_sched_1Mr3YdLkdIwHu7ixjop3qtff",
  "object": "subscription_schedule",
  "application": null,
  "canceled_at": null,
  "completed_at": null,
  "created": 1724058651,
  "current_phase": null,
  "customer": "cus_NcI8FsMbh0OeFs",
  "default_settings": {
    "application_fee_percent": null,
    "automatic_tax": {
      "enabled": false,
      "liability": null
    },
    "billing_cycle_anchor": "automatic",
    "collection_method": "charge_automatically",
    "default_payment_method": null,
    "default_source": null,
    "description": null,
    "invoice_settings": {
      "issuer": {
        "type": "self"
      }
    },
    "on_behalf_of": null,
    "transfer_data": null
  },
  "end_behavior": "release",
  "livemode": false,
  "metadata": {},
  "phases": [
    {
      "add_invoice_items": [],
      "application_fee_percent": null,
      "billing_cycle_anchor": null,
      "collection_method": null,
      "currency": "usd",
      "default_payment_method": null,
      "default_tax_rates": [],
      "description": null,
      "discounts": null,
      "end_date": 1818666418,
      "invoice_settings": null,
      "items": [
        {
          "discounts": null,
          "metadata": {},
          "plan": "price_1Mr3YcLkdIwHu7ixYCFhXHNb",
          "price": "price_1Mr3YcLkdIwHu7ixYCFhXHNb",
          "quantity": 1,
          "tax_rates": []
        }
      ],
      "metadata": {},
      "on_behalf_of": null,
      "proration_behavior": "create_prorations",
      "start_date": 1787130418,
      "transfer_data": null,
      "trial_end": null
    }
  ],
  "released_at": null,
  "released_subscription": null,
  "renewal_interval": null,
  "status": "not_started",
  "subscription": null,
  "test_clock": null
}
List all schedules 
Retrieves the list of your subscription schedules.

Parameters

customer
string
Only return subscription schedules for the given customer.

More parameters
Expand all

canceled_at
object
Only return subscription schedules that were created canceled the given date interval.

Hide child parameters

canceled_at.gt
integer
Minimum value to filter by (exclusive)


canceled_at.gte
integer
Minimum value to filter by (inclusive)


canceled_at.lt
integer
Maximum value to filter by (exclusive)


canceled_at.lte
integer
Maximum value to filter by (inclusive)


completed_at
object
Only return subscription schedules that completed during the given date interval.

Hide child parameters

completed_at.gt
integer
Minimum value to filter by (exclusive)


completed_at.gte
integer
Minimum value to filter by (inclusive)


completed_at.lt
integer
Maximum value to filter by (exclusive)


completed_at.lte
integer
Maximum value to filter by (inclusive)


created
object
Only return subscription schedules that were created during the given date interval.

Hide child parameters

created.gt
integer
Minimum value to filter by (exclusive)


created.gte
integer
Minimum value to filter by (inclusive)


created.lt
integer
Maximum value to filter by (exclusive)


created.lte
integer
Maximum value to filter by (inclusive)


ending_before
string
A cursor for use in pagination. ending_before is an object ID that defines your place in the list. For instance, if you make a list request and receive 100 objects, starting with obj_bar, your subsequent call can include ending_before=obj_bar in order to fetch the previous page of the list.


limit
integer
A limit on the number of objects to be returned. Limit can range between 1 and 100, and the default is 10.


released_at
object
Only return subscription schedules that were released during the given date interval.

Hide child parameters

released_at.gt
integer
Minimum value to filter by (exclusive)


released_at.gte
integer
Minimum value to filter by (inclusive)


released_at.lt
integer
Maximum value to filter by (exclusive)


released_at.lte
integer
Maximum value to filter by (inclusive)


scheduled
boolean
Only return subscription schedules that have not started yet.


starting_after
string
A cursor for use in pagination. starting_after is an object ID that defines your place in the list. For instance, if you make a list request and receive 100 objects, ending with obj_foo, your subsequent call can include starting_after=obj_foo in order to fetch the next page of the list.

Returns
A dictionary with a data property that contains an array of up to limit subscription schedules, starting after subscription schedule starting_after. Each entry in the array is a separate subscription schedule object. If no more subscription schedules are available, the resulting array will be empty.

GET 
/v1/subscription_schedules
Server-side language

cURL
curl -G https://api.stripe.com/v1/subscription_schedules \
  -u "sk_test_51RM6VhD6e5d6MzhOErw0xgWv3b0ikASKaAqR7F99xvXKSBqemvGHSaRItYmzP6bOZpFWjFg7M1xYHHf9QeoA11WX00Q44iCIDB:" \
  -d limit=3
Response
{
  "object": "list",
  "url": "/v1/subscription_schedules",
  "has_more": false,
  "data": [
    {
      "id": "sub_sched_1Mr3YdLkdIwHu7ixjop3qtff",
      "object": "subscription_schedule",
      "application": null,
      "canceled_at": null,
      "completed_at": null,
      "created": 1724058651,
      "current_phase": null,
      "customer": "cus_NcI8FsMbh0OeFs",
      "default_settings": {
        "application_fee_percent": null,
        "automatic_tax": {
          "enabled": false,
          "liability": null
        },
        "billing_cycle_anchor": "automatic",
        "collection_method": "charge_automatically",
        "default_payment_method": null,
        "default_source": null,
        "description": null,
        "invoice_settings": {
          "issuer": {
            "type": "self"
          }
        },
        "on_behalf_of": null,
        "transfer_data": null
      },
      "end_behavior": "release",
      "livemode": false,
      "metadata": {},
      "phases": [
        {
          "add_invoice_items": [],
          "application_fee_percent": null,
          "billing_cycle_anchor": null,
          "collection_method": null,
          "currency": "usd",
          "default_payment_method": null,
          "default_tax_rates": [],
          "description": null,
          "discounts": null,
          "end_date": 1818666418,
          "invoice_settings": null,
          "items": [
            {
              "discounts": null,
              "metadata": {},
              "plan": "price_1Mr3YcLkdIwHu7ixYCFhXHNb",
              "price": "price_1Mr3YcLkdIwHu7ixYCFhXHNb",
              "quantity": 1,
              "tax_rates": []
            }
          ],
          "metadata": {},
          "on_behalf_of": null,
          "proration_behavior": "create_prorations",
          "start_date": 1787130418,
          "transfer_data": null,
          "trial_end": null
        }
      ],
      "released_at": null,
      "released_subscription": null,
      "renewal_interval": null,
      "status": "not_started",
      "subscription": null,
      "test_clock": null
    }
  ]
}
Cancel a schedule 
Cancels a subscription schedule and its associated subscription immediately (if the subscription schedule has an active subscription). A subscription schedule can only be canceled if its status is not_started or active.

Parameters

invoice_now
boolean
If the subscription schedule is active, indicates if a final invoice will be generated that contains any un-invoiced metered usage and new/pending proration invoice items. Defaults to true.

More parameters
Expand all

prorate
boolean
If the subscription schedule is active, indicates if the cancellation should be prorated. Defaults to true.

Returns
The canceled subscription_schedule object. Its status will be canceled and canceled_at will be the current time.

POST 
/v1/subscription_schedules/:id/cancel
Server-side language

cURL
curl -X POST https://api.stripe.com/v1/subscription_schedules/sub_sched_1Mr3owLkdIwHu7ix38CXMudt/cancel \
  -u "sk_test_51RM6VhD6e5d6MzhOErw0xgWv3b0ikASKaAqR7F99xvXKSBqemvGHSaRItYmzP6bOZpFWjFg7M1xYHHf9QeoA11WX00Q44iCIDB:"
Response
{
  "id": "sub_sched_1Mr3owLkdIwHu7ix38CXMudt",
  "object": "subscription_schedule",
  "application": null,
  "canceled_at": 1680114847,
  "completed_at": null,
  "created": 1680114846,
  "current_phase": null,
  "customer": "cus_NcIPFRC981NmaY",
  "default_settings": {
    "application_fee_percent": null,
    "automatic_tax": {
      "enabled": false,
      "liability": null
    },
    "billing_cycle_anchor": "automatic",
    "collection_method": "charge_automatically",
    "default_payment_method": null,
    "default_source": null,
    "description": null,
    "invoice_settings": {
      "issuer": {
        "type": "self"
      }
    },
    "on_behalf_of": null,
    "transfer_data": null
  },
  "end_behavior": "release",
  "livemode": false,
  "metadata": {},
  "phases": [
    {
      "add_invoice_items": [],
      "application_fee_percent": null,
      "billing_cycle_anchor": null,
      "collection_method": null,
      "currency": "usd",
      "default_payment_method": null,
      "default_tax_rates": [],
      "description": null,
      "discounts": null,
      "end_date": 1712339228,
      "invoice_settings": null,
      "items": [
        {
          "metadata": {},
          "plan": "price_1Mr3owLkdIwHu7ix0RyYpQzk",
          "price": "price_1Mr3owLkdIwHu7ix0RyYpQzk",
          "quantity": 1,
          "tax_rates": []
        }
      ],
      "metadata": {},
      "on_behalf_of": null,
      "proration_behavior": "create_prorations",
      "start_date": 1680716828,
      "transfer_data": null,
      "trial_end": null
    }
  ],
  "released_at": null,
  "released_subscription": null,
  "renewal_interval": null,
  "status": "canceled",
  "subscription": null,
  "test_clock": null
}
Release a schedule 
Releases the subscription schedule immediately, which will stop scheduling of its phases, but leave any existing subscription in place. A schedule can only be released if its status is not_started or active. If the subscription schedule is currently associated with a subscription, releasing it will remove its subscription property and set the subscription’s ID to the released_subscription property.

Parameters
No parameters.

More parameters
Expand all

preserve_cancel_date
boolean
Keep any cancellation on the subscription that the schedule has set

Returns
The released subscription_schedule object. Its status will be released, released_at will be the current time, and released_subscription will be the ID of the subscription the subscription schedule managed prior to being released.

POST 
/v1/subscription_schedules/:id/release
Server-side language

cURL
curl -X POST https://api.stripe.com/v1/subscription_schedules/sub_sched_1Mr3hWLkdIwHu7ixA5zxZvNI/release \
  -u "sk_test_51RM6VhD6e5d6MzhOErw0xgWv3b0ikASKaAqR7F99xvXKSBqemvGHSaRItYmzP6bOZpFWjFg7M1xYHHf9QeoA11WX00Q44iCIDB:"
Response
{
  "id": "sub_sched_1Mr3hWLkdIwHu7ixA5zxZvNI",
  "object": "subscription_schedule",
  "application": null,
  "canceled_at": null,
  "completed_at": null,
  "created": 1680114386,
  "current_phase": null,
  "customer": "cus_NcII9GZkTPAnor",
  "default_settings": {
    "application_fee_percent": null,
    "automatic_tax": {
      "enabled": false,
      "liability": null
    },
    "billing_cycle_anchor": "automatic",
    "collection_method": "charge_automatically",
    "default_payment_method": null,
    "default_source": null,
    "description": null,
    "invoice_settings": {
      "issuer": {
        "type": "self"
      }
    },
    "on_behalf_of": null,
    "transfer_data": null
  },
  "end_behavior": "release",
  "livemode": false,
  "metadata": {},
  "phases": [
    {
      "add_invoice_items": [],
      "application_fee_percent": null,
      "billing_cycle_anchor": null,
      "collection_method": null,
      "currency": "usd",
      "default_payment_method": null,
      "default_tax_rates": [],
      "description": null,
      "discounts": null,
      "end_date": 1712339228,
      "invoice_settings": null,
      "items": [
        {
          "metadata": {},
          "plan": "price_1Mr3hVLkdIwHu7ixWuJp9ew0",
          "price": "price_1Mr3hVLkdIwHu7ixWuJp9ew0",
          "quantity": 1,
          "tax_rates": []
        }
      ],
      "metadata": {},
      "on_behalf_of": null,
      "proration_behavior": "create_prorations",
      "start_date": 1680716828,
      "transfer_data": null,
      "trial_end": null
    }
  ],
  "released_at": 1680114386,
  "released_subscription": null,
  "renewal_interval": null,
  "status": "released",
  "subscription": null,
  "test_clock": null
}