# Subscription schedules

Learn about subscription schedules and how to use them.

Use [subscription schedules](https://docs.stripe.com/api/subscription_schedules.md) to automate changes to subscriptions over time. You can [create](https://docs.stripe.com/api/subscription_schedules/create.md) subscriptions directly through a schedule or you can add a schedule to an existing subscription. Use the [phases](https://docs.stripe.com/api/subscription_schedules/object.md#subscription_schedule_object-phases) attribute to define the changes you want to make to the subscription. After a schedule completes all of its phases, it completes based on its [end_behavior](https://docs.stripe.com/api/subscription_schedules/create.md#create_subscription_schedule-end_behavior).

Some changes you might want to schedule include:

- Starting a subscription on a future date
- Backdating a subscription to a past date
- Upgrading or downgrading a subscription

Subscription schedules are available in both the Stripe Billing Dashboard and the API. Here’s a quick video of how subscription schedules work in the Dashboard:

Subscription schedules in the Dashboard

The rest of this document explains subscription schedules in more detail. To see a list of examples, see the [use cases](https://docs.stripe.com/billing/subscriptions/subscription-schedules/use-cases.md) page.

## Phases 

When creating a subscription schedule, use the [phases](https://docs.stripe.com/api/subscription_schedules/object.md#subscription_schedule_object-phases) attribute to define when changes occur and what properties of the subscription to change. For example, you might offer a coupon for 50% off for the first three months of a subscription. In this scenario, you’d create a subscription schedule where the first phase is three months long and contains the 50% off coupon. In the second phase, the subscription would be reverted to the normal cost and the coupon would be removed. Phases must be sequential, meaning only one phase can be active at a given time. 

### Set the length of a phase 

The [interval](https://docs.stripe.com/api/prices/object.md#price_object-recurring-interval) of a price determines how often to bill for a subscription. For example, a monthly interval is billed every month. Phases have an [iterations](https://docs.stripe.com/api/subscription_schedules/create.md#create_subscription_schedule-phases-iterations) attribute that you use to specify how long a phase should last. Multiply this value by the interval to determine the length of the phase. If a subscription schedule uses a price with a monthly interval and you set `iterations=2`, the phase lasts for two months.

The `end_date` of one phase has to be the `start_date` for the next phase. Using `iterations` automatically sets the `start_date` and `end_date` properly. You can set these values manually, but Stripe recommends using `iterations` instead. Because manually setting the start and end dates is prone to errors, only use it for advanced use cases.

### Transition to the next phase 

Phase transitions happen automatically after the `end_date` on a phase is reached. When a phase starts, Stripe updates the subscription based on the attributes of the next phase. You can optionally enable [prorations](https://docs.stripe.com/api/subscription_schedules/object.md#subscription_schedule_object-phases-proration_behavior) to credit the user for unused items or time on the plan.

#### Proration behavior 

There are two different proration behavior settings that control how Stripe handles billing adjustments during subscription schedule changes:

1. **Schedule update proration behavior**: The top-level [`proration_behavior`](https://docs.stripe.com/api/subscription_schedules/update.md#update_subscription_schedule-proration_behavior) parameter controls how to handle prorations when updating a subscription schedule in a way that affects the current phase’s billing configuration (such as changing prices or quantities).

1. **Phase transition proration behavior**: Each phase has its own [`proration_behavior`](https://docs.stripe.com/api/subscription_schedules/object.md#subscription_schedule_object-phases-proration_behavior) attribute that controls how Stripe handles prorations when transitioning to that phase.

##### Schedule update proration behavior

When you update a subscription schedule and change the billing configuration of the `current_phase`, you can control how prorations are handled using the top-level `proration_behavior` parameter.

This parameter works similarly to the one in the [Update a subscription API](https://docs.stripe.com/api/subscriptions/update.md#update_subscription-proration_behavior), and accepts the following values:

- (default) `create_prorations`: Generate proration adjustments for billing changes.
- `none`: No prorations are created for the update.
- `always_invoice`: Generate prorations and immediately finalize an invoice.

Changes to non-billing fields (like metadata) won’t generate prorations regardless of this setting.

##### Phase transition proration behavior

Each phase can define its own `proration_behavior` to control what happens when the subscription enters that phase. This setting applies specifically to prorations generated during phase transitions and is saved as a field on the phase.

For example, if `phases[1]` increases the quantity from 1 to 3 when it starts, the `proration_behavior` on `phases[1]` determines how those prorations are handled when transitioning from `phases[0]` to `phases[1]`:

- (default) `create_prorations`: Generate pending invoice items for billing changes.
- `none`: No prorations are created when entering this phase.
- `always_invoice`: Generate prorations and immediately create an invoice when entering this phase.

If you need to change how a future phase transition handles prorations, update the `proration_behavior` setting on the future phase before it becomes active.

### Use trials 

You can add trial periods by setting [trial end](https://docs.stripe.com/api/subscription_schedules/create.md#create_subscription_schedule-phases-trial_end) on a phase. If you want the entire phase to be a trial, set the value of `trial_end` to the same time as the `end_date` of the phase. You can also set `trial_end` to a time before the `end_date` if you want to make only part of the phase a trial. When scheduling updates, you must specify the new `trial_end` on each phase.

### Complete a schedule 

Subscription schedules end after the last phase is complete. At this point, the subscription is left in place and is no longer associated with the schedule. If you want to cancel a subscription after the last phase of a schedule completes, you can set [end_behavior](https://docs.stripe.com/api/subscription_schedules/object.md#subscription_schedule_object-end_behavior) to `cancel`. The subscription’s [cancel_on_date](https://docs.stripe.com/api/subscriptions/object.md#subscription_object-cancel_at) isn’t set until the subscription transitions into the final phase.

### Phase attribute inheritance 

When a phase becomes active, all attributes set on the phase are also set on the subscription. After the phase ends, attributes remain the same unless the next phase modifies them, or if the schedule has no default setting. You can set some attributes on both schedules and phases. This includes:

| Schedule attribute                                                                                                                                                           | Phase attribute                                                                                                                                          |
| ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [default_settings.billing_thresholds](https://docs.stripe.com/api/subscription_schedules/object.md#subscription_schedule_object-default_settings-billing_thresholds)         | [phases.billing_thresholds](https://docs.stripe.com/api/subscription_schedules/object.md#subscription_schedule_object-phases-billing_thresholds)         |
| [default_settings.collection_method](https://docs.stripe.com/api/subscription_schedules/create.md#create_subscription_schedule-default_settings-collection_method)           | [phases.collection_method](https://docs.stripe.com/api/subscription_schedules/create.md#create_subscription_schedule-phases-collection_method)           |
| [default_settings.default_payment_method](https://docs.stripe.com/api/subscription_schedules/create.md#create_subscription_schedule-default_settings-default_payment_method) | [phases.default_payment_method](https://docs.stripe.com/api/subscription_schedules/create.md#create_subscription_schedule-phases-default_payment_method) |
| [default_settings.invoice_settings](https://docs.stripe.com/api/subscription_schedules/create.md#create_subscription_schedule-default_settings-invoice_settings)             | [phases.invoice_settings](https://docs.stripe.com/api/subscription_schedules/create.md#create_subscription_schedule-phases-invoice_settings)             |

If one of these attributes is defined on the schedule, it becomes the default for all phases. When the same property is defined on both the schedule and the phase, the phase attribute overrides the schedule attribute. This behavior is explained more below:

| Schedule attribute present | Phase attribute present | Outcome                                      |
| -------------------------- | ----------------------- | -------------------------------------------- |
| No                         | No                      | Defaults to the customer or account settings |
| Yes                        | No                      | Schedule attribute set                       |
| Yes                        | Yes                     | Phase attribute set                          |
| No                         | Yes                     | Phase attribute set                          |

### Use phase metadata 

You can use subscription schedule phases to set metadata on the underlying subscription. This allows you to control the metadata on a subscription with scheduled updates.

To use phase metadata with the API, set [metadata](https://docs.stripe.com/api/subscription_schedules/object.md#subscription_schedule_object-phases-metadata) on the phases of a subscription schedule. When the underlying subscription enters a phase:

* Metadata from the phase with non-empty values are _added_ to the metadata on the subscription if the keys _aren’t_ already present in the latter.
* Metadata from the phase with non-empty values are used to _update_ the metadata on the subscription if the keys _are_ already present in the latter.
* Metadata from the phase with _empty values_ are used to _unset_ the corresponding keys in the metadata on the subscription.

To unset all keys in the subscription’s metadata, update the subscription directly or unset every key individually from the phase’s metadata. Updating the underlying subscription’s metadata directly doesn’t affect the current phase’s metadata.

The following example illustrates a subscription schedule with two phases, where each phase has its own metadata:

```json
{
  ...
  "object": "subscription_schedule",
  "phases": [
    { // Phase 1
      ...
      "metadata": {
        "channel": "self-serve",
        "region": "apac",
        "upsell-products": "alpha"
      },
    },
    { // Phase 2
      ...
      "metadata": {
        "channel": "sales",
        "churn-risk": "high",
        "upsell-products": ""
      },
    }
  ],
}
```

When this schedule creates a new subscription and the subscription enters `Phase 1`, the three keys in `Phase 1` metadata are added to the subscription’s metadata. Hence, the subscription in `Phase 1` has the following metadata:

```json
{
  ...
  "object": "subscription",
  "metadata": {
    "channel": "self-serve",
    "region": "apac",
    "upsell-products": "alpha"
  },
}
```

When the subscription enters `Phase 2`, the subscription’s metadata is updated:

* The value of `channel` is updated because a value is specified on the phase’s metadata and the subscription already has metadata with that key.
* The value of `region` is unchanged because it’s not specified on the phase.
* `churn-risk` is added because this is a new key.
* `upsell-products` is unset because an empty value is specified on the phase.

Hence, the subscription in `Phase 2` has the following metadata:

```json
{
  ...
  "object": "subscription",
  "metadata": {
    "channel": "sales",
    "region": "apac",
    "churn-risk": "high"
  }
}
```

You can add, edit, and remove metadata entries in each phase of a subscription schedule using the [Billing Dashboard subscription editor](https://dashboard.stripe.com/subscriptions?create=subscription). When a new phase activates, the metadata of the subscription object updates to match that phase’s metadata.

Learn how to [copy subscription metadata onto subscription invoices](https://docs.stripe.com/billing/invoices/subscription.md#subscription-metadata).

## Create subscription schedules 

The [use cases](https://docs.stripe.com/billing/subscriptions/subscription-schedules/use-cases.md) page has more thorough examples but below is a basic example of creating a subscription schedule using a customer. Creating a schedule this way automatically creates the subscription as well.

Unlike when you create a subscription directly, the first invoice of a subscription schedule with `collection_method` set to `charge_automatically` behaves like a recurring invoice and _isn’t_ immediately finalized at the time the schedule’s subscription is created. The invoice begins in a `draft` status and is finalized by Stripe [about 1 hour after creation](https://docs.stripe.com/billing/subscriptions/webhooks.md#understand).

This means that, for example, creating a charge-automatically subscription schedule with `start_date=now` also creates a subscription and an invoice in the `draft` status. This gives you a 1-hour window to [make edits to the invoice](https://docs.stripe.com/api/invoices/update.md). Later, the invoice is auto-advanced into the `open` or `paid` status depending on the outcome of the asynchronous payment attempt at finalization time.

```dotnet
StripeConfiguration.ApiKey = "<<secret key>>";

var options = new SubscriptionScheduleCreateOptions
{
    Customer = "cus_GBHHxuvBvO26Ea",
    StartDate = SubscriptionScheduleStartDate.Now,
    EndBehavior = "release",
    Phases = new List<SubscriptionSchedulePhaseOptions>
    {
        new SubscriptionSchedulePhaseOptions
        {
            Items = new List<SubscriptionSchedulePhaseItemOptions>
            {
                new SubscriptionSchedulePhaseItemOptions
                {
                    Price = "price_1GqNdGAJVYItwOKqEHb",
                    Quantity = 1,
                },
            },
            Iterations = 12,
        },
    },
};
var service = new SubscriptionScheduleService();
SubscriptionSchedule subscriptionSchedule = service.Create(options);
```

```go
stripe.Key = "<<secret key>>"

params := &stripe.SubscriptionScheduleParams{
  Customer: stripe.String("cus_GBHHxuvBvO26Ea"),
  StartDateNow: true,
  EndBehavior: stripe.String(string(stripe.SubscriptionScheduleEndBehaviorRelease)),
  Phases: []*stripe.SubscriptionSchedulePhaseParams{
    &stripe.SubscriptionSchedulePhaseParams{
      Items: []*stripe.SubscriptionSchedulePhaseItemParams{
        &stripe.SubscriptionSchedulePhaseItemParams{
          Price: stripe.String("price_1GqNdGAJVYItwOKqEHb"),
          Quantity: stripe.Int64(1),
        },
      },
      Iterations: stripe.Int64(12),
    },
  },
};
result, err := subscriptionschedule.New(params);
```

```java
Stripe.apiKey = "<<secret key>>";

SubscriptionScheduleCreateParams params =
  SubscriptionScheduleCreateParams.builder()
    .setCustomer("cus_GBHHxuvBvO26Ea")
    .setStartDate(SubscriptionScheduleCreateParams.StartDate.NOW)
    .setEndBehavior(SubscriptionScheduleCreateParams.EndBehavior.RELEASE)
    .addPhase(
      SubscriptionScheduleCreateParams.Phase.builder()
        .addItem(
          SubscriptionScheduleCreateParams.Phase.Item.builder()
            .setPrice("price_1GqNdGAJVYItwOKqEHb")
            .setQuantity(1L)
            .build()
        )
        .setIterations(12L)
        .build()
    )
    .build();

SubscriptionSchedule subscriptionSchedule = SubscriptionSchedule.create(params);
```

```node
const stripe = require('stripe')('<<secret key>>');

const subscriptionSchedule = await stripe.subscriptionSchedules.create({
  customer: 'cus_GBHHxuvBvO26Ea',
  start_date: 'now',
  end_behavior: 'release',
  phases: [
    {
      items: [
        {
          price: 'price_1GqNdGAJVYItwOKqEHb',
          quantity: 1,
        },
      ],
      iterations: 12,
    },
  ],
});
```

```python
import stripe
stripe.api_key = "<<secret key>>"

subscription_schedule = stripe.SubscriptionSchedule.create(
  customer="cus_GBHHxuvBvO26Ea",
  start_date="now",
  end_behavior="release",
  phases=[{"items": [{"price": "price_1GqNdGAJVYItwOKqEHb", "quantity": 1}], "iterations": 12}],
)
```

```php
$stripe = new \Stripe\StripeClient('<<secret key>>');

$subscriptionSchedule = $stripe->subscriptionSchedules->create([
  'customer' => 'cus_GBHHxuvBvO26Ea',
  'start_date' => 'now',
  'end_behavior' => 'release',
  'phases' => [
    [
      'items' => [
        [
          'price' => 'price_1GqNdGAJVYItwOKqEHb',
          'quantity' => 1,
        ],
      ],
      'iterations' => 12,
    ],
  ],
]);
```

```ruby
Stripe.api_key = '<<secret key>>'

subscription_schedule = Stripe::SubscriptionSchedule.create({
  customer: 'cus_GBHHxuvBvO26Ea',
  start_date: 'now',
  end_behavior: 'release',
  phases: [
    {
      items: [
        {
          price: 'price_1GqNdGAJVYItwOKqEHb',
          quantity: 1,
        },
      ],
      iterations: 12,
    },
  ],
})
```

This schedule:

- Starts as soon as it’s created.
- Sets the subscription to one instance of the product at `price_1GqNdGAJVYItwOKqEHb`.
- Goes through 12 iterations and then releases the subscription from the schedule.

You can also create subscription schedules by passing a subscription ID:

```dotnet
StripeConfiguration.ApiKey = "<<secret key>>";

var options = new SubscriptionScheduleCreateOptions { FromSubscription = "sub_GB98WOvaRAWPl6" };
var service = new SubscriptionScheduleService();
SubscriptionSchedule subscriptionSchedule = service.Create(options);
```

```go
stripe.Key = "<<secret key>>"

params := &stripe.SubscriptionScheduleParams{FromSubscription: stripe.String("sub_GB98WOvaRAWPl6")};
result, err := subscriptionschedule.New(params);
```

```java
Stripe.apiKey = "<<secret key>>";

SubscriptionScheduleCreateParams params =
  SubscriptionScheduleCreateParams.builder().setFromSubscription("sub_GB98WOvaRAWPl6").build();

SubscriptionSchedule subscriptionSchedule = SubscriptionSchedule.create(params);
```

```node
const stripe = require('stripe')('<<secret key>>');

const subscriptionSchedule = await stripe.subscriptionSchedules.create({
  from_subscription: 'sub_GB98WOvaRAWPl6',
});
```

```python
import stripe
stripe.api_key = "<<secret key>>"

subscription_schedule = stripe.SubscriptionSchedule.create(from_subscription="sub_GB98WOvaRAWPl6")
```

```php
$stripe = new \Stripe\StripeClient('<<secret key>>');

$subscriptionSchedule = $stripe->subscriptionSchedules->create([
  'from_subscription' => 'sub_GB98WOvaRAWPl6',
]);
```

```ruby
Stripe.api_key = '<<secret key>>'

subscription_schedule = Stripe::SubscriptionSchedule.create({
  from_subscription: 'sub_GB98WOvaRAWPl6',
})
```

Creating a schedule this way uses attributes on the subscription to set attributes on the schedule.

Similar to other Stripe APIs, you can retrieve and update [subscription schedules](https://docs.stripe.com/api/subscription_schedules.md). You can also cancel and release them. Cancelling a subscription schedule cancels the subscription as well. If you only want to remove a schedule from a subscription, use the [release](https://docs.stripe.com/api/subscription_schedules/release.md) call.

You can create multi-phase subscription schedules without using code in the Stripe Billing subscription editor. To do so, follow these steps:

1. In the Dashboard, open the [subscription editor](https://dashboard.stripe.com/subscriptions?create=subscription).
1. Add a customer.
1. Add a price to the product selection dropdown.
1. Set a duration for the first phase of the subscription schedule.
1. Click **+ Add phase**.
1. Select your next phase duration, or just **forever** to keep the subscription going.
1. Make the required changes to your new phase. You can change the quantity, change the price, add or remove coupons, reset the billing cycle date, change proration behavior, or update metadata. If you change the metadata in a phase, it updates the subscription’s metadata when that phase activates.
1. Save the new phase.
1. Add any additional phases as needed.
1. Create a subscription.

## Update subscription schedules 

You can only update the current and future phases on subscription schedules.

You need to pass in all current and future phases when you update a subscription schedule. You also need to pass in any previously set parameters that you want to keep. Any parameters that were previously set are unset for the existing phase unless you pass those in the update request. You still receive information in the response about past phases.

```dotnet
StripeConfiguration.ApiKey = "<<secret key>>";

var options = new SubscriptionScheduleUpdateOptions
{
    Phases = new List<SubscriptionSchedulePhaseOptions>
    {
        new SubscriptionSchedulePhaseOptions
        {
            Items = new List<SubscriptionSchedulePhaseItemOptions>
            {
                new SubscriptionSchedulePhaseItemOptions { Price = "<<price>>", Quantity = 2 },
            },
            StartDate = DateTimeOffset.FromUnixTimeSeconds(1577865600).UtcDateTime,
            EndDate = DateTimeOffset.FromUnixTimeSeconds(1580544000).UtcDateTime,
        },
    },
};
var service = new SubscriptionScheduleService();
SubscriptionSchedule subscriptionSchedule = service.Update("<<subscriptionSchedule>>", options);
```

```go
stripe.Key = "<<secret key>>"

params := &stripe.SubscriptionScheduleParams{
  Phases: []*stripe.SubscriptionSchedulePhaseParams{
    &stripe.SubscriptionSchedulePhaseParams{
      Items: []*stripe.SubscriptionSchedulePhaseItemParams{
        &stripe.SubscriptionSchedulePhaseItemParams{
          Price: stripe.String("<<price>>"),
          Quantity: stripe.Int64(2),
        },
      },
      StartDate: stripe.Int64(1577865600),
      EndDate: stripe.Int64(1580544000),
    },
  },
};
result, err := subscriptionschedule.Update("<<subscriptionSchedule>>", params);
```

```java
Stripe.apiKey = "<<secret key>>";

SubscriptionSchedule resource = SubscriptionSchedule.retrieve("<<subscriptionSchedule>>");

SubscriptionScheduleUpdateParams params =
  SubscriptionScheduleUpdateParams.builder()
    .addPhase(
      SubscriptionScheduleUpdateParams.Phase.builder()
        .addItem(
          SubscriptionScheduleUpdateParams.Phase.Item.builder()
            .setPrice("<<price>>")
            .setQuantity(2L)
            .build()
        )
        .setStartDate(1577865600L)
        .setEndDate(1580544000L)
        .build()
    )
    .build();

SubscriptionSchedule subscriptionSchedule = resource.update(params);
```

```node
const stripe = require('stripe')('<<secret key>>');

const subscriptionSchedule = await stripe.subscriptionSchedules.update(
  '<<subscriptionSchedule>>',
  {
    phases: [
      {
        items: [
          {
            price: '<<price>>',
            quantity: 2,
          },
        ],
        start_date: 1577865600,
        end_date: 1580544000,
      },
    ],
  }
);
```

```python
import stripe
stripe.api_key = "<<secret key>>"

subscription_schedule = stripe.SubscriptionSchedule.modify(
  "<<subscriptionSchedule>>",
  phases=[
    {
      "items": [{"price": "<<price>>", "quantity": 2}],
      "start_date": 1577865600,
      "end_date": 1580544000,
    },
  ],
)
```

```php
$stripe = new \Stripe\StripeClient('<<secret key>>');

$subscriptionSchedule = $stripe->subscriptionSchedules->update(
  '<<subscriptionSchedule>>',
  [
    'phases' => [
      [
        'items' => [
          [
            'price' => '<<price>>',
            'quantity' => 2,
          ],
        ],
        'start_date' => 1577865600,
        'end_date' => 1580544000,
      ],
    ],
  ]
);
```

```ruby
Stripe.api_key = '<<secret key>>'

subscription_schedule = Stripe::SubscriptionSchedule.update(
  '<<subscriptionSchedule>>',
  {
    phases: [
      {
        items: [
          {
            price: '<<price>>',
            quantity: 2,
          },
        ],
        start_date: 1577865600,
        end_date: 1580544000,
      },
    ],
  },
)
```

You can also end the current phase immediately and start a new phase. This moves the active phase to the past and immediately applies the new phase to the subscription. The example below ends the current phase and starts a new phase:

```dotnet
StripeConfiguration.ApiKey = "<<secret key>>";

var options = new SubscriptionScheduleUpdateOptions
{
    Phases = new List<SubscriptionSchedulePhaseOptions>
    {
        new SubscriptionSchedulePhaseOptions
        {
            Items = new List<SubscriptionSchedulePhaseItemOptions>
            {
                new SubscriptionSchedulePhaseItemOptions { Price = "<<price>>", Quantity = 1 },
            },
            StartDate = DateTimeOffset.FromUnixTimeSeconds(1577865600).UtcDateTime,
            EndDate = SubscriptionSchedulePhaseEndDate.Now,
        },
        new SubscriptionSchedulePhaseOptions
        {
            Items = new List<SubscriptionSchedulePhaseItemOptions>
            {
                new SubscriptionSchedulePhaseItemOptions { Price = "<<price>>", Quantity = 2 },
            },
            StartDate = SubscriptionSchedulePhaseStartDate.Now,
            EndDate = DateTimeOffset.FromUnixTimeSeconds(1580544000).UtcDateTime,
        },
    },
};
var service = new SubscriptionScheduleService();
SubscriptionSchedule subscriptionSchedule = service.Update("<<subscriptionSchedule>>", options);
```

```go
stripe.Key = "<<secret key>>"

params := &stripe.SubscriptionScheduleParams{
  Phases: []*stripe.SubscriptionSchedulePhaseParams{
    &stripe.SubscriptionSchedulePhaseParams{
      Items: []*stripe.SubscriptionSchedulePhaseItemParams{
        &stripe.SubscriptionSchedulePhaseItemParams{
          Price: stripe.String("<<price>>"),
          Quantity: stripe.Int64(1),
        },
      },
      StartDate: stripe.Int64(1577865600),
      EndDateNow: true,
    },
    &stripe.SubscriptionSchedulePhaseParams{
      Items: []*stripe.SubscriptionSchedulePhaseItemParams{
        &stripe.SubscriptionSchedulePhaseItemParams{
          Price: stripe.String("<<price>>"),
          Quantity: stripe.Int64(2),
        },
      },
      StartDateNow: true,
      EndDate: stripe.Int64(1580544000),
    },
  },
};
result, err := subscriptionschedule.Update("<<subscriptionSchedule>>", params);
```

```java
Stripe.apiKey = "<<secret key>>";

SubscriptionSchedule resource = SubscriptionSchedule.retrieve("<<subscriptionSchedule>>");

SubscriptionScheduleUpdateParams params =
  SubscriptionScheduleUpdateParams.builder()
    .addPhase(
      SubscriptionScheduleUpdateParams.Phase.builder()
        .addItem(
          SubscriptionScheduleUpdateParams.Phase.Item.builder()
            .setPrice("<<price>>")
            .setQuantity(1L)
            .build()
        )
        .setStartDate(1577865600L)
        .setEndDate(SubscriptionScheduleUpdateParams.Phase.EndDate.NOW)
        .build()
    )
    .addPhase(
      SubscriptionScheduleUpdateParams.Phase.builder()
        .addItem(
          SubscriptionScheduleUpdateParams.Phase.Item.builder()
            .setPrice("<<price>>")
            .setQuantity(2L)
            .build()
        )
        .setStartDate(SubscriptionScheduleUpdateParams.Phase.StartDate.NOW)
        .setEndDate(1580544000L)
        .build()
    )
    .build();

SubscriptionSchedule subscriptionSchedule = resource.update(params);
```

```node
const stripe = require('stripe')('<<secret key>>');

const subscriptionSchedule = await stripe.subscriptionSchedules.update(
  '<<subscriptionSchedule>>',
  {
    phases: [
      {
        items: [
          {
            price: '<<price>>',
            quantity: 1,
          },
        ],
        start_date: 1577865600,
        end_date: 'now',
      },
      {
        items: [
          {
            price: '<<price>>',
            quantity: 2,
          },
        ],
        start_date: 'now',
        end_date: 1580544000,
      },
    ],
  }
);
```

```python
import stripe
stripe.api_key = "<<secret key>>"

subscription_schedule = stripe.SubscriptionSchedule.modify(
  "<<subscriptionSchedule>>",
  phases=[
    {"items": [{"price": "<<price>>", "quantity": 1}], "start_date": 1577865600, "end_date": "now"},
    {"items": [{"price": "<<price>>", "quantity": 2}], "start_date": "now", "end_date": 1580544000},
  ],
)
```

```php
$stripe = new \Stripe\StripeClient('<<secret key>>');

$subscriptionSchedule = $stripe->subscriptionSchedules->update(
  '<<subscriptionSchedule>>',
  [
    'phases' => [
      [
        'items' => [
          [
            'price' => '<<price>>',
            'quantity' => 1,
          ],
        ],
        'start_date' => 1577865600,
        'end_date' => 'now',
      ],
      [
        'items' => [
          [
            'price' => '<<price>>',
            'quantity' => 2,
          ],
        ],
        'start_date' => 'now',
        'end_date' => 1580544000,
      ],
    ],
  ]
);
```

```ruby
Stripe.api_key = '<<secret key>>'

subscription_schedule = Stripe::SubscriptionSchedule.update(
  '<<subscriptionSchedule>>',
  {
    phases: [
      {
        items: [
          {
            price: '<<price>>',
            quantity: 1,
          },
        ],
        start_date: 1577865600,
        end_date: 'now',
      },
      {
        items: [
          {
            price: '<<price>>',
            quantity: 2,
          },
        ],
        start_date: 'now',
        end_date: 1580544000,
      },
    ],
  },
)
```

To add additional phases to a subscription schedule, pass in the current phase, and then define your new phases:

```dotnet
StripeConfiguration.ApiKey = "<<secret key>>";

var options = new SubscriptionScheduleUpdateOptions
{
    Phases = new List<SubscriptionSchedulePhaseOptions>
    {
        new SubscriptionSchedulePhaseOptions
        {
            Items = new List<SubscriptionSchedulePhaseItemOptions>
            {
                new SubscriptionSchedulePhaseItemOptions { Price = "<<price>>", Quantity = 1 },
            },
            StartDate = DateTimeOffset.FromUnixTimeSeconds(1577865600).UtcDateTime,
            EndDate = DateTimeOffset.FromUnixTimeSeconds(1580544000).UtcDateTime,
        },
        new SubscriptionSchedulePhaseOptions
        {
            Items = new List<SubscriptionSchedulePhaseItemOptions>
            {
                new SubscriptionSchedulePhaseItemOptions { Price = "<<price>>", Quantity = 2 },
            },
            Iterations = 1,
        },
    },
};
var service = new SubscriptionScheduleService();
SubscriptionSchedule subscriptionSchedule = service.Update("<<subscriptionSchedule>>", options);
```

```go
stripe.Key = "<<secret key>>"

params := &stripe.SubscriptionScheduleParams{
  Phases: []*stripe.SubscriptionSchedulePhaseParams{
    &stripe.SubscriptionSchedulePhaseParams{
      Items: []*stripe.SubscriptionSchedulePhaseItemParams{
        &stripe.SubscriptionSchedulePhaseItemParams{
          Price: stripe.String("<<price>>"),
          Quantity: stripe.Int64(1),
        },
      },
      StartDate: stripe.Int64(1577865600),
      EndDate: stripe.Int64(1580544000),
    },
    &stripe.SubscriptionSchedulePhaseParams{
      Items: []*stripe.SubscriptionSchedulePhaseItemParams{
        &stripe.SubscriptionSchedulePhaseItemParams{
          Price: stripe.String("<<price>>"),
          Quantity: stripe.Int64(2),
        },
      },
      Iterations: stripe.Int64(1),
    },
  },
};
result, err := subscriptionschedule.Update("<<subscriptionSchedule>>", params);
```

```java
Stripe.apiKey = "<<secret key>>";

SubscriptionSchedule resource = SubscriptionSchedule.retrieve("<<subscriptionSchedule>>");

SubscriptionScheduleUpdateParams params =
  SubscriptionScheduleUpdateParams.builder()
    .addPhase(
      SubscriptionScheduleUpdateParams.Phase.builder()
        .addItem(
          SubscriptionScheduleUpdateParams.Phase.Item.builder()
            .setPrice("<<price>>")
            .setQuantity(1L)
            .build()
        )
        .setStartDate(1577865600L)
        .setEndDate(1580544000L)
        .build()
    )
    .addPhase(
      SubscriptionScheduleUpdateParams.Phase.builder()
        .addItem(
          SubscriptionScheduleUpdateParams.Phase.Item.builder()
            .setPrice("<<price>>")
            .setQuantity(2L)
            .build()
        )
        .setIterations(1L)
        .build()
    )
    .build();

SubscriptionSchedule subscriptionSchedule = resource.update(params);
```

```node
const stripe = require('stripe')('<<secret key>>');

const subscriptionSchedule = await stripe.subscriptionSchedules.update(
  '<<subscriptionSchedule>>',
  {
    phases: [
      {
        items: [
          {
            price: '<<price>>',
            quantity: 1,
          },
        ],
        start_date: 1577865600,
        end_date: 1580544000,
      },
      {
        items: [
          {
            price: '<<price>>',
            quantity: 2,
          },
        ],
        iterations: 1,
      },
    ],
  }
);
```

```python
import stripe
stripe.api_key = "<<secret key>>"

subscription_schedule = stripe.SubscriptionSchedule.modify(
  "<<subscriptionSchedule>>",
  phases=[
    {
      "items": [{"price": "<<price>>", "quantity": 1}],
      "start_date": 1577865600,
      "end_date": 1580544000,
    },
    {"items": [{"price": "<<price>>", "quantity": 2}], "iterations": 1},
  ],
)
```

```php
$stripe = new \Stripe\StripeClient('<<secret key>>');

$subscriptionSchedule = $stripe->subscriptionSchedules->update(
  '<<subscriptionSchedule>>',
  [
    'phases' => [
      [
        'items' => [
          [
            'price' => '<<price>>',
            'quantity' => 1,
          ],
        ],
        'start_date' => 1577865600,
        'end_date' => 1580544000,
      ],
      [
        'items' => [
          [
            'price' => '<<price>>',
            'quantity' => 2,
          ],
        ],
        'iterations' => 1,
      ],
    ],
  ]
);
```

```ruby
Stripe.api_key = '<<secret key>>'

subscription_schedule = Stripe::SubscriptionSchedule.update(
  '<<subscriptionSchedule>>',
  {
    phases: [
      {
        items: [
          {
            price: '<<price>>',
            quantity: 1,
          },
        ],
        start_date: 1577865600,
        end_date: 1580544000,
      },
      {
        items: [
          {
            price: '<<price>>',
            quantity: 2,
          },
        ],
        iterations: 1,
      },
    ],
  },
)
```

You can update existing subscriptions to have future subscription schedule phases using the Stripe Billing subscription editor. To do so, follow these steps:

1. In the Dashboard, go to the [Subscriptions](https://dashboard.stripe.com/subscriptions) page, select an existing subscription, and click **Actions** > **Update subscription**.
1. Set a duration for the current phase of the subscription schedule by selecting an end date.
1. Click **+Add phase**.
1. Select your next phase duration, or just select **forever** to keep the subscription going.
1. Make the required changes to your new phase. You can change the quantity, change the price, add or remove coupons, reset the billing cycle date, change proration behavior, or update metadata. If you change the metadata in a phase, it updates the subscription’s metadata when that phase activates.
1. Save the new phase.
1. Add any additional phases as needed.
1. Create a subscription.

## Preview an invoice

Use the [schedule](https://docs.stripe.com/api/invoices/create_preview.md#create_create_preview-schedule) parameter in the [create preview](https://docs.stripe.com/api/invoices/create_preview.md) to preview the next invoice for a subscription schedule.

```dotnet
StripeConfiguration.ApiKey = "<<secret key>>";

var options = new InvoiceCreatePreviewOptions { Schedule = "<<subscriptionSchedule>>" };
var service = new InvoiceService();
Invoice invoice = service.CreatePreview(options);
```

```go
stripe.Key = "<<secret key>>"

params := &stripe.InvoiceCreatePreviewParams{Schedule: stripe.String("<<subscriptionSchedule>>")};
result, err := invoice.CreatePreview(params);
```

```java
Stripe.apiKey = "<<secret key>>";

InvoiceCreatePreviewParams params =
  InvoiceCreatePreviewParams.builder().setSchedule("<<subscriptionSchedule>>").build();

Invoice invoice = Invoice.createPreview(params);
```

```node
const stripe = require('stripe')('<<secret key>>');

const invoice = await stripe.invoices.createPreview({
  schedule: '<<subscriptionSchedule>>',
});
```

```python
import stripe
stripe.api_key = "<<secret key>>"

invoice = stripe.Invoice.create_preview(schedule="<<subscriptionSchedule>>")
```

```php
$stripe = new \Stripe\StripeClient('<<secret key>>');

$invoice = $stripe->invoices->createPreview(['schedule' => '<<subscriptionSchedule>>']);
```

```ruby
Stripe.api_key = '<<secret key>>'

invoice = Stripe::Invoice.create_preview({schedule: '<<subscriptionSchedule>>'})
```

### Previewing schedule creation and updates

Use the parameters in [schedule_details](https://docs.stripe.com/api/invoices/create_preview.md#create_create_preview-schedule_details) to preview creating or updating a subscription schedule. Pass an existing [schedule](https://docs.stripe.com/api/invoices/create_preview.md#create_create_preview-schedule) to tell Stripe whether it’s a creation or an update.

## Additional considerations 

Subscription schedules generally follow the same restrictions as subscriptions, but also introduce some of their own restrictions. Additionally, the interaction between subscription schedules and subscriptions can produce unexpected behavior. Review the following sections to understand limitations, product behavior, and general best practices when using subscription schedules.

### Restrictions 

* You can only define up to 10 current or future phases at a time on a subscription schedule. Past phases don’t count against this limit.
* Subscription schedule phases also follow [the same restrictions as subscriptions](https://docs.stripe.com/billing/subscriptions/multiple-products.md#restrictions) when creating subscription schedule phases with multiple items.

### Dashboard limitations 

You can create and update subscription schedules without code in the [Dashboard](https://dashboard.stripe.com/test/subscriptions).

In the Dashboard, you can set the following settings globally across all phases, but not on a per phase basis:

- Billing thresholds
- Payment methods
- Invoice settings
- Subscription description
- Trial days (only works with the first phase)

The following parameters aren’t supported in the Dashboard:

- Subscription schedule metadata
- Phase item metadata
- Currency
- All Connect parameters

### Subscription updates when a schedule is attached 

Use [subscription schedules](https://docs.stripe.com/api/subscription_schedules.md) to modify subscriptions automatically when time passes and the schedule’s next phase is entered. Some changes that you make directly to the subscription propagate to the subscription schedule’s phases, but some don’t. This means that any modifications you make directly to the subscription might be overwritten by the subscription schedule when the next phase is entered.

When scheduling changes to a subscription, follow these best practices:

* If a subscription has a subscription schedule attached, use the [Subscription Schedule](https://docs.stripe.com/api/subscription_schedules.md) API to modify the subscription, instead of the [Subscriptions](https://docs.stripe.com/api/subscriptions.md#subscriptions) API.
* Store the subscription schedule IDs alongside the subscription ID for future API updates. The subscription schedule ID returns when you [use the API to create it](https://docs.stripe.com/api/subscription_schedules/create.md) or through the [subscription_schedule.created](https://docs.stripe.com/api/events/types.md#event_types-subscription_schedule.created) webhook when Stripe creates it automatically, such as when a customer scheduled a downgrade in the [Customer Portal](https://docs.stripe.com/customer-management/configure-portal.md#configure-subscription-management).
* Discard the subscription schedule IDs when a subscription schedule is released. You can make changes to the subscriptions directly or create a new subscription schedule. The subscription schedule ID is returned when [released with the API](https://docs.stripe.com/api/subscription_schedules/release.md) or through the [subscription_schedule.released](https://docs.stripe.com/api/events/types.md#event_types-subscription_schedule.released) webhook event when the subscription schedule releases.
* Use the Dashboard to modify subscriptions, if possible, which automatically updates any attached subscription schedule.

Specifically, when you change any of the following subscription attributes directly on a subscription, this action might automatically create a new subscription schedule phase:

* `discounts`
* `tax_rates`
* `items`
* `trial_end`, `trial_settings`, `trial_start`
* `application_fee_percent`
* `add_invoice_items`
* `automatic_tax`

For example, consider a subscription with two items. The subscription has a subscription schedule attached with a single phase, mirroring the current state of the subscription. If you [use the API to delete](https://docs.stripe.com/api/subscription_items/delete.md) one of the items, this automatically splits the attached subscription schedule’s phase into two phases:

1. The phase that just ended and had two subscription items
1. The new phase that has just one item on the subscription

When subscription schedule phases automatically split, the following properties are copied from the current phase to the new phase:

* `proration_behavior`
* `billing_cycle_anchor`
* `cancel_at_period_end`
* `description`
* `metadata`
* `pause_collection`

Additionally, Stripe might copy the following top-level subscription attributes to the subscription schedule or its [`default_settings`](https://docs.stripe.com/api/subscription_schedules/object.md#subscription_schedule_object-default_settings):

| Subscription attribute    | Copied to new subscription schedule phase | Copied to subscription schedule `default_settings` |
| ------------------------- | ----------------------------------------- | -------------------------------------------------- |
| `coupon`                  | Supported                                 | Unsupported                                        |
| `trial_end`               | Supported                                 | Unsupported                                        |
| `tax_rates`               | Supported                                 | Unsupported                                        |
| `application_fee_percent` | Supported                                 | Supported                                          |
| `discounts`               | Supported                                 | Unsupported                                        |
| `collection_method`       | Supported                                 | Supported                                          |
| `invoice_settings`        | Supported                                 | Supported                                          |
| `default_payment_method`  | Supported                                 | Supported                                          |
| `default_source`          | Supported                                 | Supported                                          |
| `transfer_data`           | Supported                                 | Supported                                          |
| `on_behalf_of`            | Supported                                 | Supported                                          |
| `currency`                | Supported                                 | Unsupported                                        |
| `add_invoice_items`       | Supported                                 | Unsupported                                        |
| `automatic_tax`           | Supported                                 | Supported                                          |
| `items.prices`            | Supported                                 | Unsupported                                        |
| `billing_thresholds`      | Supported                                 | Supported                                          |

Updates to subscription `metadata` aren’t propagated to an attached subscription schedule.

## See Also

* [Subscription Schedules use cases](https://docs.stripe.com/billing/subscriptions/subscription-schedules/use-cases.md)